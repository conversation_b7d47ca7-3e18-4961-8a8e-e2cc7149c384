(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[439],{1666:e=>{e.exports={style:{fontFamily:"'Inter', 'Inter Fallback'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},3655:(e,t,n)=>{"use strict";n.d(t,{hO:()=>l,sG:()=>u});var r=n(2115),o=n(7650),a=n(9708),i=n(5155),u=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,a.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...a}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,i.jsx)(o?n:t,{...a,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function l(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4516:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4783:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},7489:(e,t,n)=>{"use strict";n.d(t,{b:()=>c});var r=n(2115),o=n(3655),a=n(5155),i="horizontal",u=["horizontal","vertical"],l=r.forwardRef((e,t)=>{var n;let{decorative:r,orientation:l=i,...c}=e,s=(n=l,u.includes(n))?l:i;return(0,a.jsx)(o.sG.div,{"data-orientation":s,...r?{role:"none"}:{"aria-orientation":"vertical"===s?s:void 0,role:"separator"},...c,ref:t})});l.displayName="Separator";var c=l},8883:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9420:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9601:(e,t,n)=>{"use strict";n.d(t,{bm:()=>to,UC:()=>tr,hJ:()=>tn,ZL:()=>tt,bL:()=>e7,l9:()=>te});var r,o,a,i=n(2115),u=n.t(i,2);function l(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}var c=n(6101),s=n(5155),d=globalThis?.document?i.useLayoutEffect:()=>{},f=u[" useId ".trim().toString()]||(()=>void 0),v=0;function p(e){let[t,n]=i.useState(f());return d(()=>{e||n(e=>e??String(v++))},[e]),e||(t?`radix-${t}`:"")}var m=u[" useInsertionEffect ".trim().toString()]||d;Symbol("RADIX:SYNC_STATE");var h=n(3655);function g(e){let t=i.useRef(e);return i.useEffect(()=>{t.current=e}),i.useMemo(()=>(...e)=>t.current?.(...e),[])}var y="dismissableLayer.update",b=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),E=i.forwardRef((e,t)=>{var n,r;let{disableOutsidePointerEvents:a=!1,onEscapeKeyDown:u,onPointerDownOutside:d,onFocusOutside:f,onInteractOutside:v,onDismiss:p,...m}=e,E=i.useContext(b),[C,R]=i.useState(null),x=null!=(r=null==C?void 0:C.ownerDocument)?r:null==(n=globalThis)?void 0:n.document,[,S]=i.useState({}),O=(0,c.s)(t,e=>R(e)),A=Array.from(E.layers),[D]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),M=A.indexOf(D),T=C?A.indexOf(C):-1,k=E.layersWithOutsidePointerEventsDisabled.size>0,L=T>=M,P=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),o=i.useRef(!1),a=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!o.current){let t=function(){N("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);o.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>o.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));L&&!n&&(null==d||d(e),null==v||v(e),e.defaultPrevented||null==p||p())},x),I=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=g(e),o=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!o.current&&N("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==f||f(e),null==v||v(e),e.defaultPrevented||null==p||p())},x);return!function(e,t=globalThis?.document){let n=g(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{T===E.layers.size-1&&(null==u||u(e),!e.defaultPrevented&&p&&(e.preventDefault(),p()))},x),i.useEffect(()=>{if(C)return a&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(o=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(C)),E.layers.add(C),w(),()=>{a&&1===E.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=o)}},[C,x,a,E]),i.useEffect(()=>()=>{C&&(E.layers.delete(C),E.layersWithOutsidePointerEventsDisabled.delete(C),w())},[C,E]),i.useEffect(()=>{let e=()=>S({});return document.addEventListener(y,e),()=>document.removeEventListener(y,e)},[]),(0,s.jsx)(h.sG.div,{...m,ref:O,style:{pointerEvents:k?L?"auto":"none":void 0,...e.style},onFocusCapture:l(e.onFocusCapture,I.onFocusCapture),onBlurCapture:l(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:l(e.onPointerDownCapture,P.onPointerDownCapture)})});function w(){let e=new CustomEvent(y);document.dispatchEvent(e)}function N(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,h.hO)(a,i):a.dispatchEvent(i)}E.displayName="DismissableLayer",i.forwardRef((e,t)=>{let n=i.useContext(b),r=i.useRef(null),o=(0,c.s)(t,r);return i.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(h.sG.div,{...e,ref:o})}).displayName="DismissableLayerBranch";var C="focusScope.autoFocusOnMount",R="focusScope.autoFocusOnUnmount",x={bubbles:!1,cancelable:!0},S=i.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:o,onUnmountAutoFocus:a,...u}=e,[l,d]=i.useState(null),f=g(o),v=g(a),p=i.useRef(null),m=(0,c.s)(t,e=>d(e)),y=i.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;i.useEffect(()=>{if(r){let e=function(e){if(y.paused||!l)return;let t=e.target;l.contains(t)?p.current=t:D(p.current,{select:!0})},t=function(e){if(y.paused||!l)return;let t=e.relatedTarget;null!==t&&(l.contains(t)||D(p.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&D(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,y.paused]),i.useEffect(()=>{if(l){M.add(y);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(C,x);l.addEventListener(C,f),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(D(r,{select:t}),document.activeElement!==n)return}(O(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&D(l))}return()=>{l.removeEventListener(C,f),setTimeout(()=>{let t=new CustomEvent(R,x);l.addEventListener(R,v),l.dispatchEvent(t),t.defaultPrevented||D(null!=e?e:document.body,{select:!0}),l.removeEventListener(R,v),M.remove(y)},0)}}},[l,f,v,y]);let b=i.useCallback(e=>{if(!n&&!r||y.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=O(e);return[A(t,e),A(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&D(a,{select:!0})):(e.preventDefault(),n&&D(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,y.paused]);return(0,s.jsx)(h.sG.div,{tabIndex:-1,...u,ref:m,onKeyDown:b})});function O(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function A(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function D(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}S.displayName="FocusScope";var M=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=T(e,t)).unshift(t)},remove(t){var n;null==(n=(e=T(e,t))[0])||n.resume()}}}();function T(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var k=n(7650),L=i.forwardRef((e,t)=>{var n,r;let{container:o,...a}=e,[u,l]=i.useState(!1);d(()=>l(!0),[]);let c=o||u&&(null==(r=globalThis)||null==(n=r.document)?void 0:n.body);return c?k.createPortal((0,s.jsx)(h.sG.div,{...a,ref:t}),c):null});L.displayName="Portal";var P=e=>{let{present:t,children:n}=e,r=function(e){var t,n;let[r,o]=i.useState(),a=i.useRef(null),u=i.useRef(e),l=i.useRef("none"),[c,s]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},i.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return i.useEffect(()=>{let e=I(a.current);l.current="mounted"===c?e:"none"},[c]),d(()=>{let t=a.current,n=u.current;if(n!==e){let r=l.current,o=I(t);e?s("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?s("UNMOUNT"):n&&r!==o?s("ANIMATION_OUT"):s("UNMOUNT"),u.current=e}},[e,s]),d(()=>{if(r){var e;let t,n=null!=(e=r.ownerDocument.defaultView)?e:window,o=e=>{let o=I(a.current).includes(e.animationName);if(e.target===r&&o&&(s("ANIMATION_END"),!u.current)){let e=r.style.animationFillMode;r.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===r.style.animationFillMode&&(r.style.animationFillMode=e)})}},i=e=>{e.target===r&&(l.current=I(a.current))};return r.addEventListener("animationstart",i),r.addEventListener("animationcancel",o),r.addEventListener("animationend",o),()=>{n.clearTimeout(t),r.removeEventListener("animationstart",i),r.removeEventListener("animationcancel",o),r.removeEventListener("animationend",o)}}s("ANIMATION_END")},[r,s]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:i.useCallback(e=>{a.current=e?getComputedStyle(e):null,o(e)},[])}}(t),o="function"==typeof n?n({present:r.isPresent}):i.Children.only(n),a=(0,c.s)(r.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(o));return"function"==typeof n||r.isPresent?i.cloneElement(o,{ref:a}):null};function I(e){return(null==e?void 0:e.animationName)||"none"}P.displayName="Presence";var j=0;function _(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var F=function(){return(F=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function W(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var U=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),B="width-before-scroll-bar";function $(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var G="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,z=new WeakMap;function K(e){return e}var X=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=K),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),n={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),n}}}});return o.options=F({async:!0,ssr:!1},e),o}(),Y=function(){},q=i.forwardRef(function(e,t){var n,r,o,a,u=i.useRef(null),l=i.useState({onScrollCapture:Y,onWheelCapture:Y,onTouchMoveCapture:Y}),c=l[0],s=l[1],d=e.forwardProps,f=e.children,v=e.className,p=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,b=e.noIsolation,E=e.inert,w=e.allowPinchZoom,N=e.as,C=e.gapMode,R=W(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=(n=[u,t],r=function(e){return n.forEach(function(t){return $(t,e)})},(o=(0,i.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,a=o.facade,G(function(){var e=z.get(a);if(e){var t=new Set(e),r=new Set(n),o=a.current;t.forEach(function(e){r.has(e)||$(e,null)}),r.forEach(function(e){t.has(e)||$(e,o)})}z.set(a,n)},[n]),a),S=F(F({},R),c);return i.createElement(i.Fragment,null,m&&i.createElement(g,{sideCar:X,removeScrollBar:p,shards:h,noRelative:y,noIsolation:b,inert:E,setCallbacks:s,allowPinchZoom:!!w,lockRef:u,gapMode:C}),d?i.cloneElement(i.Children.only(f),F(F({},S),{ref:x})):i.createElement(void 0===N?"div":N,F({},S,{className:v,ref:x}),f))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:B,zeroRight:U};var Z=function(e){var t=e.sideCar,n=W(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return i.createElement(r,F({},n))};Z.isSideCarExport=!0;var H=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=a||n.nc;return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},V=function(){var e=H();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},J=function(){var e=V();return function(t){return e(t.styles,t.dynamic),null}},Q={left:0,top:0,right:0,gap:0},ee=function(e){return parseInt(e||"",10)||0},et=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[ee(n),ee(r),ee(o)]},en=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return Q;var t=et(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},er=J(),eo="data-scroll-locked",ea=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(eo,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(U," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(B," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(U," .").concat(U," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(B," .").concat(B," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(eo,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},ei=function(){var e=parseInt(document.body.getAttribute(eo)||"0",10);return isFinite(e)?e:0},eu=function(){i.useEffect(function(){return document.body.setAttribute(eo,(ei()+1).toString()),function(){var e=ei()-1;e<=0?document.body.removeAttribute(eo):document.body.setAttribute(eo,e.toString())}},[])},el=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;eu();var a=i.useMemo(function(){return en(o)},[o]);return i.createElement(er,{styles:ea(a,!t,o,n?"":"!important")})},ec=!1;if("undefined"!=typeof window)try{var es=Object.defineProperty({},"passive",{get:function(){return ec=!0,!0}});window.addEventListener("test",es,es),window.removeEventListener("test",es,es)}catch(e){ec=!1}var ed=!!ec&&{passive:!1},ef=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},ev=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),ep(e,r)){var o=em(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},ep=function(e,t){return"v"===e?ef(t,"overflowY"):ef(t,"overflowX")},em=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},eh=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,l=n.target,c=t.contains(l),s=!1,d=u>0,f=0,v=0;do{if(!l)break;var p=em(e,l),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&ep(e,l)&&(f+=h,v+=m);var g=l.parentNode;l=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},eg=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ey=function(e){return[e.deltaX,e.deltaY]},eb=function(e){return e&&"current"in e?e.current:e},eE=0,ew=[];let eN=(r=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(eE++)[0],a=i.useState(J)[0],u=i.useRef(e);i.useEffect(function(){u.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eb),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var l=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=eg(e),i=n.current,l="deltaX"in e?e.deltaX:i[0]-a[0],c="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=ev(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=ev(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var v=r.current||o;return eh(v,t,e,"h"===v?l:c,!0)},[]),c=i.useCallback(function(e){if(ew.length&&ew[ew.length-1]===a){var n="deltaY"in e?ey(e):eg(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(eb).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?l(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=i.useCallback(function(e){n.current=eg(e),r.current=void 0},[]),f=i.useCallback(function(t){s(t.type,ey(t),t.target,l(t,e.lockRef.current))},[]),v=i.useCallback(function(t){s(t.type,eg(t),t.target,l(t,e.lockRef.current))},[]);i.useEffect(function(){return ew.push(a),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",c,ed),document.addEventListener("touchmove",c,ed),document.addEventListener("touchstart",d,ed),function(){ew=ew.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,ed),document.removeEventListener("touchmove",c,ed),document.removeEventListener("touchstart",d,ed)}},[]);var p=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,p?i.createElement(el,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},X.useMedium(r),Z);var eC=i.forwardRef(function(e,t){return i.createElement(q,F({},e,{ref:t,sideCar:eN}))});eC.classNames=q.classNames;var eR=new WeakMap,ex=new WeakMap,eS={},eO=0,eA=function(e){return e&&(e.host||eA(e.parentNode))},eD=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=eA(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eS[n]||(eS[n]=new WeakMap);var a=eS[n],i=[],u=new Set,l=new Set(o),c=function(e){!e||u.has(e)||(u.add(e),c(e.parentNode))};o.forEach(c);var s=function(e){!e||l.has(e)||Array.prototype.forEach.call(e.children,function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,l=(eR.get(e)||0)+1,c=(a.get(e)||0)+1;eR.set(e,l),a.set(e,c),i.push(e),1===l&&o&&ex.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),u.clear(),eO++,function(){i.forEach(function(e){var t=eR.get(e)-1,o=a.get(e)-1;eR.set(e,t),a.set(e,o),t||(ex.has(e)||e.removeAttribute(r),ex.delete(e)),o||e.removeAttribute(n)}),--eO||(eR=new WeakMap,eR=new WeakMap,ex=new WeakMap,eS={})}},eM=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),eD(r,o,n,"aria-hidden")):function(){return null}},eT=n(9708),ek="Dialog",[eL,eP]=function(e,t=[]){let n=[],r=()=>{let t=n.map(e=>i.createContext(e));return function(n){let r=n?.[e]||t;return i.useMemo(()=>({[`__scope${e}`]:{...n,[e]:r}}),[n,r])}};return r.scopeName=e,[function(t,r){let o=i.createContext(r),a=n.length;n=[...n,r];let u=t=>{let{scope:n,children:r,...u}=t,l=n?.[e]?.[a]||o,c=i.useMemo(()=>u,Object.values(u));return(0,s.jsx)(l.Provider,{value:c,children:r})};return u.displayName=t+"Provider",[u,function(n,u){let l=u?.[e]?.[a]||o,c=i.useContext(l);if(c)return c;if(void 0!==r)return r;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let r=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return i.useMemo(()=>({[`__scope${t.scopeName}`]:r}),[r])}};return n.scopeName=t.scopeName,n}(r,...t)]}(ek),[eI,ej]=eL(ek),e_=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:o,onOpenChange:a,modal:u=!0}=e,l=i.useRef(null),c=i.useRef(null),[d,f]=function({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[o,a,u]=function({defaultProp:e,onChange:t}){let[n,r]=i.useState(e),o=i.useRef(n),a=i.useRef(t);return m(()=>{a.current=t},[t]),i.useEffect(()=>{o.current!==n&&(a.current?.(n),o.current=n)},[n,o]),[n,r,a]}({defaultProp:t,onChange:n}),l=void 0!==e,c=l?e:o;{let t=i.useRef(void 0!==e);i.useEffect(()=>{let e=t.current;if(e!==l){let t=l?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=l},[l,r])}return[c,i.useCallback(t=>{if(l){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else a(t)},[l,e,a,u])]}({prop:r,defaultProp:null!=o&&o,onChange:a,caller:ek});return(0,s.jsx)(eI,{scope:t,triggerRef:l,contentRef:c,contentId:p(),titleId:p(),descriptionId:p(),open:d,onOpenChange:f,onOpenToggle:i.useCallback(()=>f(e=>!e),[f]),modal:u,children:n})};e_.displayName=ek;var eF="DialogTrigger",eW=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=ej(eF,n),a=(0,c.s)(t,o.triggerRef);return(0,s.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":e9(o.open),...r,ref:a,onClick:l(e.onClick,o.onOpenToggle)})});eW.displayName=eF;var eU="DialogPortal",[eB,e$]=eL(eU,{forceMount:void 0}),eG=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:o}=e,a=ej(eU,t);return(0,s.jsx)(eB,{scope:t,forceMount:n,children:i.Children.map(r,e=>(0,s.jsx)(P,{present:n||a.open,children:(0,s.jsx)(L,{asChild:!0,container:o,children:e})}))})};eG.displayName=eU;var ez="DialogOverlay",eK=i.forwardRef((e,t)=>{let n=e$(ez,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=ej(ez,e.__scopeDialog);return a.modal?(0,s.jsx)(P,{present:r||a.open,children:(0,s.jsx)(eY,{...o,ref:t})}):null});eK.displayName=ez;var eX=(0,eT.TL)("DialogOverlay.RemoveScroll"),eY=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=ej(ez,n);return(0,s.jsx)(eC,{as:eX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,s.jsx)(h.sG.div,{"data-state":e9(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eq="DialogContent",eZ=i.forwardRef((e,t)=>{let n=e$(eq,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=ej(eq,e.__scopeDialog);return(0,s.jsx)(P,{present:r||a.open,children:a.modal?(0,s.jsx)(eH,{...o,ref:t}):(0,s.jsx)(eV,{...o,ref:t})})});eZ.displayName=eq;var eH=i.forwardRef((e,t)=>{let n=ej(eq,e.__scopeDialog),r=i.useRef(null),o=(0,c.s)(t,n.contentRef,r);return i.useEffect(()=>{let e=r.current;if(e)return eM(e)},[]),(0,s.jsx)(eJ,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:l(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:l(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:l(e.onFocusOutside,e=>e.preventDefault())})}),eV=i.forwardRef((e,t)=>{let n=ej(eq,e.__scopeDialog),r=i.useRef(!1),o=i.useRef(!1);return(0,s.jsx)(eJ,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var a,i;null==(a=e.onCloseAutoFocus)||a.call(e,t),t.defaultPrevented||(r.current||null==(i=n.triggerRef.current)||i.focus(),t.preventDefault()),r.current=!1,o.current=!1},onInteractOutside:t=>{var a,i;null==(a=e.onInteractOutside)||a.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let u=t.target;(null==(i=n.triggerRef.current)?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eJ=i.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:o,onCloseAutoFocus:a,...u}=e,l=ej(eq,n),d=i.useRef(null),f=(0,c.s)(t,d);return i.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:_()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:_()),j++,()=>{1===j&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),j--}},[]),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:o,onUnmountAutoFocus:a,children:(0,s.jsx)(E,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":e9(l.open),...u,ref:f,onDismiss:()=>l.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(e4,{titleId:l.titleId}),(0,s.jsx)(e8,{contentRef:d,descriptionId:l.descriptionId})]})]})}),eQ="DialogTitle";i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=ej(eQ,n);return(0,s.jsx)(h.sG.h2,{id:o.titleId,...r,ref:t})}).displayName=eQ;var e0="DialogDescription";i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=ej(e0,n);return(0,s.jsx)(h.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=e0;var e1="DialogClose",e2=i.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=ej(e1,n);return(0,s.jsx)(h.sG.button,{type:"button",...r,ref:t,onClick:l(e.onClick,()=>o.onOpenChange(!1))})});function e9(e){return e?"open":"closed"}e2.displayName=e1;var e6="DialogTitleWarning",[e5,e3]=function(e,t){let n=i.createContext(t),r=e=>{let{children:t,...r}=e,o=i.useMemo(()=>r,Object.values(r));return(0,s.jsx)(n.Provider,{value:o,children:t})};return r.displayName=e+"Provider",[r,function(r){let o=i.useContext(n);if(o)return o;if(void 0!==t)return t;throw Error(`\`${r}\` must be used within \`${e}\``)}]}(e6,{contentName:eq,titleName:eQ,docsSlug:"dialog"}),e4=e=>{let{titleId:t}=e,n=e3(e6),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return i.useEffect(()=>{t&&(document.getElementById(t)||console.error(r))},[r,t]),null},e8=e=>{let{contentRef:t,descriptionId:n}=e,r=e3("DialogDescriptionWarning"),o="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return i.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(o))},[o,t,n]),null},e7=e_,te=eW,tt=eG,tn=eK,tr=eZ,to=e2}}]);