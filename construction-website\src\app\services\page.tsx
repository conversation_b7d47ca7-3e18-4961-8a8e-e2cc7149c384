import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Construction Services - ConstructCo | Residential, Commercial & Infrastructure",
  description: "Comprehensive construction services in Nepal including residential homes, commercial buildings, infrastructure projects, renovations, and interior design. Quality construction with 10+ years experience.",
  keywords: "construction services nepal, residential construction, commercial buildings, infrastructure projects, renovation services, interior design nepal",
};
import {
  Home,
  Building,
  Construction,
  Hammer,
  Palette,
  Wrench,
  CheckCircle,
  ArrowRight,
  Clock,
  Shield,
  Award,
  Users
} from "lucide-react";

const services = [
  {
    id: "residential",
    title: "Residential Construction",
    icon: Home,
    description: "Custom homes, apartments, and residential complexes built to perfection with modern amenities and sustainable practices.",
    features: [
      "Custom home design and construction",
      "Apartment and condominium complexes",
      "Residential renovations and extensions",
      "Interior design and finishing",
      "Landscaping and outdoor spaces",
      "Energy-efficient building solutions"
    ],
    projects: "80+ Completed",
    duration: "3-12 months",
    warranty: "10 years structural"
  },
  {
    id: "commercial",
    title: "Commercial Buildings",
    icon: Building,
    description: "Office buildings, retail spaces, and commercial complexes designed for functionality and aesthetic appeal.",
    features: [
      "Office building construction",
      "Retail and shopping complexes",
      "Warehouses and industrial facilities",
      "Hotels and hospitality projects",
      "Educational institutions",
      "Healthcare facilities"
    ],
    projects: "45+ Completed",
    duration: "6-18 months",
    warranty: "15 years structural"
  },
  {
    id: "infrastructure",
    title: "Infrastructure Projects",
    icon: Construction,
    description: "Roads, bridges, and essential infrastructure development that connects communities across Nepal.",
    features: [
      "Road construction and maintenance",
      "Bridge and overpass construction",
      "Water supply and drainage systems",
      "Retaining walls and earthworks",
      "Public facility construction",
      "Urban planning and development"
    ],
    projects: "25+ Completed",
    duration: "12-36 months",
    warranty: "20 years structural"
  },
  {
    id: "renovation",
    title: "Renovation & Maintenance",
    icon: Hammer,
    description: "Comprehensive renovation services to modernize and maintain existing structures.",
    features: [
      "Building renovations and upgrades",
      "Structural repairs and reinforcement",
      "Seismic retrofitting",
      "Roof repairs and replacements",
      "Plumbing and electrical upgrades",
      "Regular maintenance services"
    ],
    projects: "100+ Completed",
    duration: "1-6 months",
    warranty: "5 years workmanship"
  },
  {
    id: "interior",
    title: "Interior & Exterior Design",
    icon: Palette,
    description: "Complete design solutions that transform spaces into functional and beautiful environments.",
    features: [
      "Interior space planning and design",
      "Exterior facade design",
      "Kitchen and bathroom remodeling",
      "Flooring and wall treatments",
      "Lighting design and installation",
      "Furniture and fixture selection"
    ],
    projects: "60+ Completed",
    duration: "2-8 months",
    warranty: "3 years materials"
  },
  {
    id: "consultation",
    title: "Construction Consultation",
    icon: Users,
    description: "Expert consultation services for project planning, feasibility studies, and construction management.",
    features: [
      "Project feasibility studies",
      "Construction planning and scheduling",
      "Cost estimation and budgeting",
      "Quality control and inspection",
      "Permit and regulatory assistance",
      "Project management services"
    ],
    projects: "200+ Consultations",
    duration: "Ongoing support",
    warranty: "Professional liability"
  }
];

const processSteps = [
  {
    step: "01",
    title: "Initial Consultation",
    description: "We discuss your vision, requirements, and budget to understand your project needs.",
    icon: Users
  },
  {
    step: "02", 
    title: "Design & Planning",
    description: "Our team creates detailed plans and designs tailored to your specifications.",
    icon: Palette
  },
  {
    step: "03",
    title: "Project Execution",
    description: "Skilled craftsmen bring your project to life with quality materials and precision.",
    icon: Hammer
  },
  {
    step: "04",
    title: "Quality Assurance",
    description: "Rigorous quality checks ensure every detail meets our high standards.",
    icon: Shield
  }
];

export default function Services() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <Wrench className="w-4 h-4 mr-2" />
              Our Services
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Comprehensive Construction{" "}
              <span className="text-orange-400">Solutions</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              From residential homes to large-scale infrastructure projects, we deliver 
              quality construction services across all sectors in Nepal.
            </p>
          </div>
        </div>
      </section>

      {/* Services Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-8">
            {services.map((service, index) => (
              <Card key={index} id={service.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="p-3 bg-blue-100 rounded-full">
                      <service.icon className="h-8 w-8 text-blue-600" />
                    </div>
                    <div>
                      <CardTitle className="text-2xl text-slate-900">
                        {service.title}
                      </CardTitle>
                      <div className="flex items-center space-x-4 mt-2 text-sm text-slate-600">
                        <span className="flex items-center">
                          <Award className="h-4 w-4 mr-1" />
                          {service.projects}
                        </span>
                        <span className="flex items-center">
                          <Clock className="h-4 w-4 mr-1" />
                          {service.duration}
                        </span>
                      </div>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-700 mb-6 leading-relaxed">
                    {service.description}
                  </p>
                  <div className="space-y-3 mb-6">
                    {service.features.map((feature, featureIndex) => (
                      <div key={featureIndex} className="flex items-center space-x-3">
                        <CheckCircle className="h-4 w-4 text-green-500 flex-shrink-0" />
                        <span className="text-slate-700 text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>
                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="text-sm text-slate-600">
                      <Shield className="h-4 w-4 inline mr-1" />
                      {service.warranty}
                    </div>
                    <Button asChild>
                      <Link href="/contact">
                        Get Quote
                        <ArrowRight className="ml-2 h-4 w-4" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Our Construction Process
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              A systematic approach that ensures quality, efficiency, and client satisfaction
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {processSteps.map((step, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="flex justify-center mb-4">
                    <div className="relative">
                      <div className="w-16 h-16 bg-blue-600 rounded-full flex items-center justify-center">
                        <step.icon className="h-8 w-8 text-white" />
                      </div>
                      <div className="absolute -top-2 -right-2 w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                        {step.step}
                      </div>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-slate-900">
                    {step.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Why Choose Our Services */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
                Why Choose Our Construction Services?
              </h2>
              <div className="space-y-4">
                {[
                  "Licensed and certified construction professionals",
                  "Quality materials from trusted suppliers",
                  "Comprehensive project management",
                  "Transparent pricing with no hidden costs",
                  "On-time delivery with quality guarantee",
                  "Post-construction support and maintenance",
                  "Eco-friendly and sustainable building practices",
                  "Compliance with all safety and building codes"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-slate-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="bg-slate-100 rounded-lg p-8 text-center">
              <div className="mb-6">
                <div className="text-5xl font-bold text-blue-600 mb-2">300+</div>
                <div className="text-xl text-slate-700">Successful Projects</div>
              </div>
              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-orange-600">98%</div>
                  <div className="text-sm text-slate-600">On-Time Delivery</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-orange-600">100%</div>
                  <div className="text-sm text-slate-600">Client Satisfaction</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Start Your Construction Project?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Get a free consultation and detailed quote for your construction needs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-orange-500 hover:bg-orange-600">
              <Link href="/contact">
                Get Free Quote
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Link href="/projects">View Our Work</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
