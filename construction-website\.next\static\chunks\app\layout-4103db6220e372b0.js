(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>l});var a=s(5155);s(2115);var n=s(9708),r=s(2085),i=s(9434);let o=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:r,asChild:l=!1,...d}=e,c=l?n.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:s,size:r,className:t})),...d})}},347:()=>{},1780:(e,t,s)=>{"use strict";s.d(t,{Header:()=>N});var a=s(5155),n=s(6874),r=s.n(n),i=s(2115),o=s(285),l=s(9601),d=s(4416),c=s(9434);function h(e){let{...t}=e;return(0,a.jsx)(l.bL,{"data-slot":"sheet",...t})}function x(e){let{...t}=e;return(0,a.jsx)(l.l9,{"data-slot":"sheet-trigger",...t})}function u(e){let{...t}=e;return(0,a.jsx)(l.ZL,{"data-slot":"sheet-portal",...t})}function m(e){let{className:t,...s}=e;return(0,a.jsx)(l.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function f(e){let{className:t,children:s,side:n="right",...r}=e;return(0,a.jsxs)(u,{children:[(0,a.jsx)(m,{}),(0,a.jsxs)(l.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...r,children:[s,(0,a.jsxs)(l.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(d.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var p=s(9420),b=s(8883),v=s(4516),g=s(4783);let j=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Projects",href:"/projects"},{name:"Gallery",href:"/gallery"},{name:"Contact",href:"/contact"}];function N(){let[e,t]=(0,i.useState)(!1);return(0,a.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60",children:[(0,a.jsx)("div",{className:"bg-slate-900 text-white py-2",children:(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center text-sm",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(p.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"+977-1-4567890"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(b.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"<EMAIL>"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(v.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:"Kathmandu, Nepal"})]})]})})}),(0,a.jsx)("div",{className:"container mx-auto px-4",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsxs)(r(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,a.jsx)("span",{className:"font-bold text-xl text-slate-900",children:"ConstructCo"})]}),(0,a.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:j.map(e=>(0,a.jsx)(r(),{href:e.href,className:"text-slate-700 hover:text-blue-600 font-medium transition-colors",children:e.name},e.name))}),(0,a.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:(0,a.jsx)(o.$,{asChild:!0,className:"bg-orange-500 hover:bg-orange-600",children:(0,a.jsx)(r(),{href:"/contact",children:"Get Quote"})})}),(0,a.jsxs)(h,{open:e,onOpenChange:t,children:[(0,a.jsx)(x,{asChild:!0,className:"md:hidden",children:(0,a.jsx)(o.$,{variant:"ghost",size:"icon",children:(0,a.jsx)(g.A,{className:"h-6 w-6"})})}),(0,a.jsx)(f,{side:"right",className:"w-[300px] sm:w-[400px]",children:(0,a.jsxs)("nav",{className:"flex flex-col space-y-4 mt-8",children:[j.map(e=>(0,a.jsx)(r(),{href:e.href,className:"text-slate-700 hover:text-blue-600 font-medium transition-colors py-2",onClick:()=>t(!1),children:e.name},e.name)),(0,a.jsx)(o.$,{asChild:!0,className:"bg-orange-500 hover:bg-orange-600 mt-4",children:(0,a.jsx)(r(),{href:"/contact",children:"Get Quote"})})]})})]})]})})]})}},1876:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,1666,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,1780)),Promise.resolve().then(s.bind(s,2346))},2346:(e,t,s)=>{"use strict";s.d(t,{Separator:()=>i});var a=s(5155);s(2115);var n=s(7489),r=s(9434);function i(e){let{className:t,orientation:s="horizontal",decorative:i=!0,...o}=e;return(0,a.jsx)(n.b,{"data-slot":"separator",decorative:i,orientation:s,className:(0,r.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...o})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var a=s(2596),n=s(9688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}}},e=>{e.O(0,[258,455,874,439,441,964,358],()=>e(e.s=1876)),_N_E=e.O()}]);