{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/app/gallery/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Gallery - ConstructCo | Construction Project Photos Nepal\",\n  description: \"View our construction project gallery showcasing quality workmanship across residential, commercial, and infrastructure projects in Nepal. Visual journey of construction excellence.\",\n  keywords: \"construction gallery nepal, project photos, building images, construction work gallery, before after construction\",\n};\n\nexport default function GalleryLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}