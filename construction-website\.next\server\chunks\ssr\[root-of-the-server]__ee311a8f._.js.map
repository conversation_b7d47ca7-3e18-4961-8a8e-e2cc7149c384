{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"inter_9e72d27f-module__JKMi0a__className\",\n  \"variable\": \"inter_9e72d27f-module__JKMi0a__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/inter_9e72d27f.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Inter%22,%22arguments%22:[{%22subsets%22:[%22latin%22],%22variable%22:%22--font-inter%22}],%22variableName%22:%22inter%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Inter', 'Inter Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,qJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,qJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,qJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header.tsx <module evaluation>\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,2DACA", "debugId": null}}, {"offset": {"line": 44, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Header = registerClientReference(\n    function() { throw new Error(\"Attempted to call Head<PERSON>() from the server but <PERSON><PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/header.tsx\",\n    \"Header\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,uCACA", "debugId": null}}, {"offset": {"line": 56, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 64, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA", "debugId": null}}, {"offset": {"line": 145, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA", "debugId": null}}, {"offset": {"line": 157, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 165, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/footer.tsx"], "sourcesContent": ["import Link from \"next/link\";\nimport { Button } from \"@/components/ui/button\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { \n  Phone, \n  Mail, \n  MapPin, \n  Facebook, \n  Twitter, \n  Instagram, \n  Linkedin,\n  Download\n} from \"lucide-react\";\n\nconst quickLinks = [\n  { name: \"Home\", href: \"/\" },\n  { name: \"About Us\", href: \"/about\" },\n  { name: \"Services\", href: \"/services\" },\n  { name: \"Projects\", href: \"/projects\" },\n  { name: \"Gallery\", href: \"/gallery\" },\n  { name: \"Careers\", href: \"/careers\" },\n];\n\nconst services = [\n  { name: \"Residential Construction\", href: \"/services#residential\" },\n  { name: \"Commercial Buildings\", href: \"/services#commercial\" },\n  { name: \"Infrastructure\", href: \"/services#infrastructure\" },\n  { name: \"Renovation\", href: \"/services#renovation\" },\n];\n\nexport function Footer() {\n  return (\n    <footer className=\"bg-slate-900 text-white\">\n      <div className=\"container mx-auto px-4 py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-lg\">C</span>\n              </div>\n              <span className=\"font-bold text-xl\">ConstructCo</span>\n            </div>\n            <p className=\"text-slate-300 text-sm leading-relaxed\">\n              Building Nepal&apos;s future with quality construction, innovative design,\n              and reliable infrastructure solutions for over 10 years.\n            </p>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center space-x-2 text-sm\">\n                <Phone className=\"h-4 w-4 text-orange-500\" />\n                <span>+977-1-4567890</span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm\">\n                <Mail className=\"h-4 w-4 text-orange-500\" />\n                <span><EMAIL></span>\n              </div>\n              <div className=\"flex items-center space-x-2 text-sm\">\n                <MapPin className=\"h-4 w-4 text-orange-500\" />\n                <span>Kathmandu, Nepal</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold text-lg\">Quick Links</h3>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <Link \n                    href={link.href}\n                    className=\"text-slate-300 hover:text-orange-500 transition-colors text-sm\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold text-lg\">Our Services</h3>\n            <ul className=\"space-y-2\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <Link \n                    href={service.href}\n                    className=\"text-slate-300 hover:text-orange-500 transition-colors text-sm\"\n                  >\n                    {service.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact & Social */}\n          <div className=\"space-y-4\">\n            <h3 className=\"font-semibold text-lg\">Connect With Us</h3>\n            <div className=\"flex space-x-3\">\n              <Button size=\"icon\" variant=\"outline\" className=\"border-slate-600 hover:bg-blue-600\">\n                <Facebook className=\"h-4 w-4\" />\n              </Button>\n              <Button size=\"icon\" variant=\"outline\" className=\"border-slate-600 hover:bg-blue-400\">\n                <Twitter className=\"h-4 w-4\" />\n              </Button>\n              <Button size=\"icon\" variant=\"outline\" className=\"border-slate-600 hover:bg-pink-600\">\n                <Instagram className=\"h-4 w-4\" />\n              </Button>\n              <Button size=\"icon\" variant=\"outline\" className=\"border-slate-600 hover:bg-blue-700\">\n                <Linkedin className=\"h-4 w-4\" />\n              </Button>\n            </div>\n            <Button className=\"bg-orange-500 hover:bg-orange-600 w-full\">\n              <Download className=\"h-4 w-4 mr-2\" />\n              Company Brochure\n            </Button>\n          </div>\n        </div>\n\n        <Separator className=\"my-8 bg-slate-700\" />\n\n        <div className=\"flex flex-col md:flex-row justify-between items-center text-sm text-slate-400\">\n          <p>&copy; 2024 ConstructCo. All rights reserved.</p>\n          <div className=\"flex space-x-4 mt-4 md:mt-0\">\n            <Link href=\"/faq\" className=\"hover:text-orange-500 transition-colors\">\n              FAQ\n            </Link>\n            <Link href=\"/privacy\" className=\"hover:text-orange-500 transition-colors\">\n              Privacy Policy\n            </Link>\n            <Link href=\"/terms\" className=\"hover:text-orange-500 transition-colors\">\n              Terms of Service\n            </Link>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAWA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAY,MAAM;IAAS;IACnC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAW,MAAM;IAAW;IACpC;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,WAAW;IACf;QAAE,MAAM;QAA4B,MAAM;IAAwB;IAClE;QAAE,MAAM;QAAwB,MAAM;IAAuB;IAC7D;QAAE,MAAM;QAAkB,MAAM;IAA2B;IAC3D;QAAE,MAAM;QAAc,MAAM;IAAuB;CACpD;AAEM,SAAS;IACd,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAoB;;;;;;;;;;;;8CAEtC,8OAAC;oCAAE,WAAU;8CAAyC;;;;;;8CAItD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAMZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,wBACb,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,IAAI;;;;;;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;sCAa3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAU,WAAU;sDAC9C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAU,WAAU;sDAC9C,cAAA,8OAAC,wMAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;sDAErB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAU,WAAU;sDAC9C,cAAA,8OAAC,4MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,8OAAC,kIAAA,CAAA,SAAM;4CAAC,MAAK;4CAAO,SAAQ;4CAAU,WAAU;sDAC9C,cAAA,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAGxB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,WAAU;;sDAChB,8OAAC,0MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;8BAM3C,8OAAC,qIAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;8BAErB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAE;;;;;;sCACH,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAO,WAAU;8CAA0C;;;;;;8CAGtE,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;8CAA0C;;;;;;8CAG1E,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAS,WAAU;8CAA0C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpF", "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Inter } from \"next/font/google\";\nimport \"./globals.css\";\nimport { Header } from \"@/components/header\";\nimport { Footer } from \"@/components/footer\";\n\nconst inter = Inter({\n  subsets: [\"latin\"],\n  variable: \"--font-inter\",\n});\n\nexport const metadata: Metadata = {\n  title: \"ConstructCo - Building Nepal's Future\",\n  description: \"Professional construction company in Nepal specializing in residential, commercial, and infrastructure projects. Quality construction with 10+ years of experience.\",\n  keywords: \"construction, Nepal, building, residential, commercial, infrastructure, renovation\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\">\n      <body className={`${inter.variable} font-sans antialiased`}>\n        <div className=\"flex flex-col min-h-screen\">\n          <Header />\n          <main className=\"flex-1\">{children}</main>\n          <Footer />\n        </div>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;;;;;;AAOO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;kBACT,cAAA,8OAAC;YAAK,WAAW,GAAG,yIAAA,CAAA,UAAK,CAAC,QAAQ,CAAC,sBAAsB,CAAC;sBACxD,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,4HAAA,CAAA,SAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,4HAAA,CAAA,SAAM;;;;;;;;;;;;;;;;;;;;;AAKjB", "debugId": null}}]}