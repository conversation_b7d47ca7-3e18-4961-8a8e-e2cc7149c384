import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import Link from "next/link";
import Image from "next/image";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "About Us - ConstructCo | Leading Construction Company in Nepal",
  description: "Learn about ConstructCo's 10+ years of construction excellence in Nepal. Our mission, vision, team, and commitment to quality construction services across residential, commercial, and infrastructure projects.",
  keywords: "about constructco, construction company nepal, building contractors kathmandu, construction team nepal, construction experience",
};
import {
  Building2,
  Users,
  Award,
  Target,
  Eye,
  Heart,
  Calendar,
  MapPin,
  CheckCircle,
  ArrowRight,
  Briefcase,
  GraduationCap,
  Shield
} from "lucide-react";

const milestones = [
  {
    year: "2014",
    title: "Company Founded",
    description: "Started with a vision to transform Nepal's construction landscape",
  },
  {
    year: "2016",
    title: "First Major Project",
    description: "Completed our first 20-unit residential complex in Kathmandu",
  },
  {
    year: "2018",
    title: "Commercial Expansion",
    description: "Expanded into commercial construction with office buildings",
  },
  {
    year: "2020",
    title: "Infrastructure Focus",
    description: "Began major infrastructure projects including roads and bridges",
  },
  {
    year: "2022",
    title: "100+ Projects",
    description: "Milestone achievement of completing over 100 successful projects",
  },
  {
    year: "2024",
    title: "Sustainable Building",
    description: "Leading eco-friendly construction practices in Nepal",
  },
];

const teamMembers = [
  {
    name: "Ramesh Shrestha",
    role: "Founder & CEO",
    experience: "15+ years",
    expertise: "Project Management, Business Development",
    icon: Briefcase,
    image: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Sita Gurung",
    role: "Chief Engineer",
    experience: "12+ years",
    expertise: "Structural Engineering, Quality Control",
    icon: GraduationCap,
    image: "https://images.unsplash.com/photo-1494790108755-2616b612b786?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
  },
  {
    name: "Prakash Thapa",
    role: "Operations Manager",
    experience: "10+ years",
    expertise: "Site Management, Safety Compliance",
    icon: Shield,
    image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80",
  },
];

const values = [
  {
    icon: Target,
    title: "Quality First",
    description: "We never compromise on quality, using only the best materials and practices.",
  },
  {
    icon: Users,
    title: "Client Focused",
    description: "Every project is tailored to meet our clients' specific needs and vision.",
  },
  {
    icon: Heart,
    title: "Integrity",
    description: "Honest communication and transparent processes in all our dealings.",
  },
  {
    icon: Award,
    title: "Excellence",
    description: "Striving for excellence in every aspect of construction and service.",
  },
];

export default function About() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        {/* Background Image */}
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            alt="Construction team working together"
            fill
            className="object-cover"
            priority
          />
          <div className="absolute inset-0 bg-slate-900/75"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center text-white">
            <Badge className="mb-6 bg-orange-500/20 border border-orange-500/30 text-orange-300">
              <Building2 className="w-4 h-4 mr-2" />
              About ConstructCo
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Building Nepal&apos;s Future with{" "}
              <span className="text-orange-400">Passion & Precision</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              For over a decade, we&apos;ve been transforming Nepal&apos;s landscape with quality construction,
              innovative design, and unwavering commitment to excellence.
            </p>
          </div>
        </div>
      </section>

      {/* Mission & Vision */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            <Card className="p-8">
              <CardContent className="p-0">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-blue-100 rounded-full mr-4">
                    <Target className="h-8 w-8 text-blue-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-slate-900">Our Mission</h2>
                </div>
                <p className="text-slate-700 leading-relaxed">
                  To deliver exceptional construction services that exceed client expectations while 
                  contributing to Nepal's infrastructure development. We are committed to building 
                  sustainable, safe, and innovative structures that stand the test of time.
                </p>
              </CardContent>
            </Card>

            <Card className="p-8">
              <CardContent className="p-0">
                <div className="flex items-center mb-6">
                  <div className="p-3 bg-orange-100 rounded-full mr-4">
                    <Eye className="h-8 w-8 text-orange-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-slate-900">Our Vision</h2>
                </div>
                <p className="text-slate-700 leading-relaxed">
                  To be Nepal's most trusted construction company, recognized for our quality, 
                  innovation, and contribution to sustainable development. We envision a future 
                  where every structure we build enhances communities and improves lives.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Company Stats */}
      <section className="py-16 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">150+</div>
              <div className="text-slate-600">Projects Completed</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">200+</div>
              <div className="text-slate-600">Happy Clients</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">10+</div>
              <div className="text-slate-600">Years Experience</div>
            </div>
            <div className="text-center">
              <div className="text-4xl font-bold text-blue-600 mb-2">50+</div>
              <div className="text-slate-600">Team Members</div>
            </div>
          </div>
        </div>
      </section>

      {/* Company Timeline */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Our Journey
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              A decade of growth, innovation, and building trust across Nepal
            </p>
          </div>
          <div className="max-w-4xl mx-auto">
            <div className="space-y-8">
              {milestones.map((milestone, index) => (
                <div key={index} className="flex items-start space-x-6">
                  <div className="flex-shrink-0">
                    <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold">
                      {milestone.year.slice(-2)}
                    </div>
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <Calendar className="h-4 w-4 text-slate-500" />
                      <span className="text-sm text-slate-500">{milestone.year}</span>
                    </div>
                    <h3 className="text-xl font-semibold text-slate-900 mb-2">
                      {milestone.title}
                    </h3>
                    <p className="text-slate-600">{milestone.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Our Values */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Our Core Values
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              The principles that guide every project and decision we make
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="flex justify-center mb-4">
                    <div className="p-4 bg-blue-100 rounded-full">
                      <value.icon className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-3 text-slate-900">
                    {value.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Leadership Team */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Leadership Team
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Experienced professionals leading Nepal's construction innovation
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                <CardContent className="p-0">
                  <div className="flex justify-center mb-6">
                    <div className="relative">
                      <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-blue-100 group-hover:border-orange-200 transition-colors duration-300">
                        <Image
                          src={member.image}
                          alt={member.name}
                          width={96}
                          height={96}
                          className="object-cover w-full h-full group-hover:scale-110 transition-transform duration-300"
                        />
                      </div>
                      <div className="absolute -bottom-2 -right-2 w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center border-2 border-white">
                        <member.icon className="h-4 w-4 text-white" />
                      </div>
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-slate-900 group-hover:text-blue-600 transition-colors">
                    {member.name}
                  </h3>
                  <p className="text-orange-600 font-medium mb-2">{member.role}</p>
                  <p className="text-slate-600 text-sm mb-3">{member.experience}</p>
                  <p className="text-slate-700 text-sm">{member.expertise}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Build Your Vision?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Let's discuss your construction project and bring your ideas to life with our expertise.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-orange-500 hover:bg-orange-600">
              <Link href="/contact">
                Start Your Project
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Link href="/projects">View Our Portfolio</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
