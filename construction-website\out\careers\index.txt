1:"$Sreact.fragment"
2:I[1780,["455","static/chunks/455-227259ab86ac26cf.js","874","static/chunks/874-437a265a67d6cfee.js","439","static/chunks/439-961d5c7f3d4d6b8d.js","177","static/chunks/app/layout-4103db6220e372b0.js"],"Header"]
3:I[7555,[],""]
4:I[1295,[],""]
10:I[8393,[],""]
:HL["/_next/static/css/3a731bf3e10580be.css","style"]
0:{"P":null,"b":"gvnORnjbgXHPUIRqqp0et","p":"","c":["","careers",""],"i":false,"f":[[["",{"children":["careers",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/3a731bf3e10580be.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","children":["$","body",null,{"className":"__variable_e8ce0c font-sans antialiased","children":["$","div",null,{"className":"flex flex-col min-h-screen","children":[["$","$L2",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"bg-slate-900 text-white","children":["$","div",null,{"className":"container mx-auto px-4 py-12","children":[["$","div",null,{"className":"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8","children":[["$","div",null,{"className":"space-y-4","children":[["$","div",null,{"className":"flex items-center space-x-2","children":[["$","div",null,{"className":"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center","children":["$","span",null,{"className":"text-white font-bold text-lg","children":"C"}]}],["$","span",null,{"className":"font-bold text-xl","children":"ConstructCo"}]]}],["$","p",null,{"className":"text-slate-300 text-sm leading-relaxed","children":"Building Nepal's future with quality construction, innovative design, and reliable infrastructure solutions for over 10 years."}],["$","div",null,{"className":"space-y-2","children":[["$","div",null,{"className":"flex items-center space-x-2 text-sm","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-phone h-4 w-4 text-orange-500","aria-hidden":"true","children":[["$","path","9njp5v",{"d":"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384"}],"$undefined"]}],["$","span",null,{"children":"+977-1-4567890"}]]}],["$","div",null,{"className":"flex items-center space-x-2 text-sm","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-mail h-4 w-4 text-orange-500","aria-hidden":"true","children":[["$","path","132q7q",{"d":"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"}],["$","rect","izxlao",{"x":"2","y":"4","width":"20","height":"16","rx":"2"}],"$undefined"]}],["$","span",null,{"children":"<EMAIL>"}]]}],["$","div",null,{"className":"flex items-center space-x-2 text-sm","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 text-orange-500","aria-hidden":"true","children":["$L5","$L6","$undefined"]}],"$L7"]}]]}]]}],"$L8","$L9","$La"]}],"$Lb","$Lc"]}]}]]}]}]}]]}],{"children":["careers","$Ld",{"children":["__PAGE__","$Le",{},null,false]},null,false]},null,false],"$Lf",false]],"m":"$undefined","G":["$10",[]],"s":false,"S":true}
11:I[6874,["874","static/chunks/874-437a265a67d6cfee.js","846","static/chunks/app/careers/page-0c44346088931e58.js"],""]
14:I[2346,["455","static/chunks/455-227259ab86ac26cf.js","874","static/chunks/874-437a265a67d6cfee.js","439","static/chunks/439-961d5c7f3d4d6b8d.js","177","static/chunks/app/layout-4103db6220e372b0.js"],"Separator"]
1b:I[9665,[],"ViewportBoundary"]
1d:I[9665,[],"MetadataBoundary"]
1e:"$Sreact.suspense"
5:["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}]
6:["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}]
7:["$","span",null,{"children":"Kathmandu, Nepal"}]
8:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Quick Links"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","Home",{"children":["$","$L11",null,{"href":"/","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Home"}]}],["$","li","About Us",{"children":["$","$L11",null,{"href":"/about","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"About Us"}]}],["$","li","Services",{"children":["$","$L11",null,{"href":"/services","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Services"}]}],["$","li","Projects",{"children":["$","$L11",null,{"href":"/projects","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Projects"}]}],["$","li","Gallery",{"children":["$","$L11",null,{"href":"/gallery","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Gallery"}]}],["$","li","Careers",{"children":["$","$L11",null,{"href":"/careers","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Careers"}]}]]}]]}]
9:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Our Services"}],["$","ul",null,{"className":"space-y-2","children":[["$","li","Residential Construction",{"children":["$","$L11",null,{"href":"/services#residential","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Residential Construction"}]}],["$","li","Commercial Buildings",{"children":["$","$L11",null,{"href":"/services#commercial","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Commercial Buildings"}]}],["$","li","Infrastructure",{"children":["$","$L11",null,{"href":"/services#infrastructure","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Infrastructure"}]}],["$","li","Renovation",{"children":["$","$L11",null,{"href":"/services#renovation","className":"text-slate-300 hover:text-orange-500 transition-colors text-sm","children":"Renovation"}]}]]}]]}]
a:["$","div",null,{"className":"space-y-4","children":[["$","h3",null,{"className":"font-semibold text-lg","children":"Connect With Us"}],["$","div",null,{"className":"flex space-x-3","children":[["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 size-9 border-slate-600 hover:bg-blue-600","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-facebook h-4 w-4","aria-hidden":"true","children":[["$","path","1jg4f8",{"d":"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"}],"$undefined"]}]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 size-9 border-slate-600 hover:bg-blue-400","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-twitter h-4 w-4","aria-hidden":"true","children":[["$","path","pff0z6",{"d":"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"}],"$undefined"]}]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 size-9 border-slate-600 hover:bg-pink-600","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-instagram h-4 w-4","aria-hidden":"true","children":[["$","rect","2e1cvw",{"width":"20","height":"20","x":"2","y":"2","rx":"5","ry":"5"}],["$","path","9exkf1",{"d":"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"}],["$","line","r4j83e",{"x1":"17.5","x2":"17.51","y1":"6.5","y2":"6.5"}],"$undefined"]}]}],["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50 size-9 border-slate-600 hover:bg-blue-700","children":"$L12"}]]}],"$L13"]}]
b:["$","$L14",null,{"className":"my-8 bg-slate-700"}]
c:["$","div",null,{"className":"flex flex-col md:flex-row justify-between items-center text-sm text-slate-400","children":[["$","p",null,{"children":"© 2024 ConstructCo. All rights reserved."}],["$","div",null,{"className":"flex space-x-4 mt-4 md:mt-0","children":[["$","$L11",null,{"href":"/faq","className":"hover:text-orange-500 transition-colors","children":"FAQ"}],["$","$L11",null,{"href":"/privacy","className":"hover:text-orange-500 transition-colors","children":"Privacy Policy"}],["$","$L11",null,{"href":"/terms","className":"hover:text-orange-500 transition-colors","children":"Terms of Service"}]]}]]}]
d:["$","$1","c",{"children":[null,["$","$L3",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L4",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}]
e:["$","$1","c",{"children":[["$","div",null,{"className":"flex flex-col","children":[["$","section",null,{"className":"relative bg-slate-900 text-white py-20","children":["$","div",null,{"className":"container mx-auto px-4","children":["$","div",null,{"className":"max-w-4xl mx-auto text-center","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent text-primary-foreground [a&]:hover:bg-primary/90 mb-6 bg-orange-500 hover:bg-orange-600","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-briefcase w-4 h-4 mr-2","aria-hidden":"true","children":[["$","path","jecpp",{"d":"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"}],["$","rect","i6l2r4",{"width":"20","height":"14","x":"2","y":"6","rx":"2"}],"$undefined"]}],"Join Our Team"]}],["$","h1",null,{"className":"text-4xl md:text-5xl font-bold mb-6","children":["Build Your Career with"," ",["$","span",null,{"className":"text-orange-400","children":"ConstructCo"}]]}],["$","p",null,{"className":"text-xl text-slate-200 leading-relaxed","children":"Join Nepal's leading construction company and be part of building the nation's future. We offer exciting opportunities for growth and professional development."}]]}]}]}],["$","section",null,{"className":"py-20 bg-white","children":["$","div",null,{"className":"container mx-auto px-4","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-slate-900 mb-4","children":"Why Choose ConstructCo?"}],["$","p",null,{"className":"text-xl text-slate-600 max-w-2xl mx-auto","children":"We're not just building structures; we're building careers and futures"}]]}],["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-4 gap-8","children":[["$","div","0",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","h3",null,{"className":"text-xl font-semibold mb-3 text-slate-900","children":"Innovation"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"We embrace new technologies and construction methods"}]]}]}],["$","div","1",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","h3",null,{"className":"text-xl font-semibold mb-3 text-slate-900","children":"Quality"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Excellence in every project we undertake"}]]}]}],["$","div","2",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","h3",null,{"className":"text-xl font-semibold mb-3 text-slate-900","children":"Teamwork"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Collaborative environment where everyone contributes"}]]}]}],["$","div","3",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6 hover:shadow-lg transition-shadow","children":"$L15"}]]}]]}]}],"$L16","$L17","$L18","$L19"]}],null,"$L1a"]}]
f:["$","$1","h",{"children":[null,[["$","$L1b",null,{"children":"$L1c"}],null],["$","$L1d",null,{"children":["$","div",null,{"hidden":true,"children":["$","$1e",null,{"fallback":null,"children":"$L1f"}]}]}]]}]
2b:I[9665,[],"OutletBoundary"]
2d:I[4911,[],"AsyncMetadataOutlet"]
12:["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-linkedin h-4 w-4","aria-hidden":"true","children":[["$","path","c2jq9f",{"d":"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"}],["$","rect","mk3on5",{"width":"4","height":"12","x":"2","y":"9"}],["$","circle","bt5ra8",{"cx":"4","cy":"4","r":"2"}],"$undefined"]}]
13:["$","button",null,{"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-primary-foreground shadow-xs h-9 px-4 py-2 has-[>svg]:px-3 bg-orange-500 hover:bg-orange-600 w-full","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-download h-4 w-4 mr-2","aria-hidden":"true","children":[["$","path","m9g1x1",{"d":"M12 15V3"}],["$","path","ih7n3h",{"d":"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}],["$","path","brsn70",{"d":"m7 10 5 5 5-5"}],"$undefined"]}],"Company Brochure"]}]
15:["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","h3",null,{"className":"text-xl font-semibold mb-3 text-slate-900","children":"Growth"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Continuous learning and professional development"}]]}]
16:["$","section",null,{"className":"py-20 bg-slate-50","children":["$","div",null,{"className":"container mx-auto px-4","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-slate-900 mb-4","children":"Employee Benefits"}],["$","p",null,{"className":"text-xl text-slate-600 max-w-2xl mx-auto","children":"We take care of our team with comprehensive benefits and support"}]]}],["$","div",null,{"className":"grid md:grid-cols-2 lg:grid-cols-3 gap-8","children":[["$","div","0",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"p-3 bg-blue-100 rounded-full mr-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-award h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","1yiouv",{"d":"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526"}],["$","circle","1vp47v",{"cx":"12","cy":"8","r":"6"}],"$undefined"]}]}],["$","h3",null,{"className":"text-lg font-semibold text-slate-900","children":"Competitive Salary"}]]}],["$","p",null,{"className":"text-slate-600 leading-relaxed","children":"Market-leading compensation packages with performance bonuses"}]]}]}],["$","div","1",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"p-3 bg-blue-100 rounded-full mr-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-heart h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","c3ymky",{"d":"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z"}],"$undefined"]}]}],["$","h3",null,{"className":"text-lg font-semibold text-slate-900","children":"Health Insurance"}]]}],["$","p",null,{"className":"text-slate-600 leading-relaxed","children":"Comprehensive health coverage for you and your family"}]]}]}],["$","div","2",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"p-3 bg-blue-100 rounded-full mr-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-graduation-cap h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","j76jl0",{"d":"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z"}],["$","path","1lu8f3",{"d":"M22 10v6"}],["$","path","1r8lef",{"d":"M6 12.5V16a6 3 0 0 0 12 0v-3.5"}],"$undefined"]}]}],["$","h3",null,{"className":"text-lg font-semibold text-slate-900","children":"Professional Development"}]]}],["$","p",null,{"className":"text-slate-600 leading-relaxed","children":"Training programs and certification support for career growth"}]]}]}],["$","div","3",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm p-6 hover:shadow-lg transition-shadow","children":"$L20"}],"$L21","$L22"]}]]}]}]
17:["$","section",null,{"className":"py-20 bg-white","children":["$","div",null,{"className":"container mx-auto px-4","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-slate-900 mb-4","children":"Current Openings"}],["$","p",null,{"className":"text-xl text-slate-600 max-w-2xl mx-auto","children":"Explore exciting career opportunities across different departments"}]]}],["$","div",null,{"className":"grid lg:grid-cols-2 gap-8","children":[["$","div","0",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm hover:shadow-lg transition-shadow","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl text-slate-900 mb-2","children":"Senior Civil Engineer"}],["$","div",null,{"className":"flex flex-wrap gap-2 mb-3","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Engineering"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Full-time"}]]}]]}]}],["$","div",null,{"className":"flex items-center space-x-4 text-sm text-slate-600","children":[["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],"Kathmandu"]}],["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],"5+ years"]}]]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","p",null,{"className":"text-slate-700 mb-4 leading-relaxed","children":"Lead structural design and project management for large-scale construction projects."}],["$","div",null,{"className":"mb-6","children":[["$","h4",null,{"className":"font-semibold text-slate-900 mb-2","children":"Requirements:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-sm text-slate-600 flex items-start","children":["$L23","Bachelor's degree in Civil Engineering"]}],"$L24","$L25","$L26"]}]]}],"$L27"]}]]}],"$L28","$L29","$L2a"]}]]}]}]
18:["$","section",null,{"className":"py-20 bg-slate-50","children":["$","div",null,{"className":"container mx-auto px-4","children":[["$","div",null,{"className":"text-center mb-16","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold text-slate-900 mb-4","children":"Application Process"}],["$","p",null,{"className":"text-xl text-slate-600 max-w-2xl mx-auto","children":"Simple and transparent hiring process"}]]}],["$","div",null,{"className":"grid md:grid-cols-4 gap-8","children":[["$","div","0",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4","children":"01"}],["$","h3",null,{"className":"text-lg font-semibold mb-3 text-slate-900","children":"Apply Online"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Submit your application through our contact form"}]]}]}],["$","div","1",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4","children":"02"}],["$","h3",null,{"className":"text-lg font-semibold mb-3 text-slate-900","children":"Initial Review"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Our HR team reviews your application and qualifications"}]]}]}],["$","div","2",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4","children":"03"}],["$","h3",null,{"className":"text-lg font-semibold mb-3 text-slate-900","children":"Interview"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Technical and cultural fit interviews with our team"}]]}]}],["$","div","3",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm text-center p-6","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4","children":"04"}],["$","h3",null,{"className":"text-lg font-semibold mb-3 text-slate-900","children":"Welcome Aboard"}],["$","p",null,{"className":"text-slate-600 text-sm leading-relaxed","children":"Onboarding and orientation to start your journey"}]]}]}]]}]]}]}]
19:["$","section",null,{"className":"py-20 bg-blue-600 text-white","children":["$","div",null,{"className":"container mx-auto px-4 text-center","children":[["$","h2",null,{"className":"text-3xl md:text-4xl font-bold mb-6","children":"Ready to Join Our Team?"}],["$","p",null,{"className":"text-xl mb-8 max-w-2xl mx-auto","children":"Take the next step in your construction career with ConstructCo."}],["$","div",null,{"className":"flex flex-col sm:flex-row gap-4 justify-center","children":[["$","$L11",null,{"href":"/contact","children":["Apply Now",["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right ml-2 h-5 w-5","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-primary-foreground shadow-xs h-10 rounded-md px-6 has-[>svg]:px-4 bg-orange-500 hover:bg-orange-600","ref":null}],["$","$L11",null,{"href":"/about","children":"Learn More About Us","data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive border bg-background shadow-xs dark:bg-input/30 dark:border-input dark:hover:bg-input/50 h-10 rounded-md px-6 has-[>svg]:px-4 border-white text-white hover:bg-white hover:text-blue-600","ref":null}]]}]]}]}]
1a:["$","$L2b",null,{"children":["$L2c",["$","$L2d",null,{"promise":"$@2e"}]]}]
20:["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"p-3 bg-blue-100 rounded-full mr-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-coffee h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","7u0qdc",{"d":"M10 2v2"}],["$","path","6buw04",{"d":"M14 2v2"}],["$","path","pwadti",{"d":"M16 8a1 1 0 0 1 1 1v8a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4V9a1 1 0 0 1 1-1h14a4 4 0 1 1 0 8h-1"}],["$","path","colzsn",{"d":"M6 2v2"}],"$undefined"]}]}],["$","h3",null,{"className":"text-lg font-semibold text-slate-900","children":"Work-Life Balance"}]]}],["$","p",null,{"className":"text-slate-600 leading-relaxed","children":"Flexible working hours and paid time off"}]]}]
21:["$","div","4",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"p-3 bg-blue-100 rounded-full mr-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-trending-up h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","box55l",{"d":"M16 7h6v6"}],["$","path","1t1m79",{"d":"m22 7-8.5 8.5-5-5L2 17"}],"$undefined"]}]}],["$","h3",null,{"className":"text-lg font-semibold text-slate-900","children":"Career Growth"}]]}],["$","p",null,{"className":"text-slate-600 leading-relaxed","children":"Clear advancement paths and leadership opportunities"}]]}]}]
22:["$","div","5",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border shadow-sm p-6 hover:shadow-lg transition-shadow","children":["$","div",null,{"data-slot":"card-content","className":"p-0","children":[["$","div",null,{"className":"flex items-center mb-4","children":[["$","div",null,{"className":"p-3 bg-blue-100 rounded-full mr-4","children":["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-shield h-6 w-6 text-blue-600","aria-hidden":"true","children":[["$","path","oel41y",{"d":"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z"}],"$undefined"]}]}],["$","h3",null,{"className":"text-lg font-semibold text-slate-900","children":"Job Security"}]]}],["$","p",null,{"className":"text-slate-600 leading-relaxed","children":"Stable employment with a growing construction company"}]]}]}]
23:["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}]
24:["$","li","1",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"5+ years of construction experience"]}]
25:["$","li","2",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Project management certification preferred"]}]
26:["$","li","3",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Strong knowledge of building codes"]}]
27:["$","$L11",null,{"href":"/contact","children":["Apply Now",["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right ml-2 h-4 w-4","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-primary-foreground shadow-xs h-9 px-4 py-2 has-[>svg]:px-3 w-full bg-blue-600 hover:bg-blue-700","ref":null}]
28:["$","div","1",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm hover:shadow-lg transition-shadow","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl text-slate-900 mb-2","children":"Site Supervisor"}],["$","div",null,{"className":"flex flex-wrap gap-2 mb-3","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Operations"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Full-time"}]]}]]}]}],["$","div",null,{"className":"flex items-center space-x-4 text-sm text-slate-600","children":[["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],"Pokhara"]}],["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],"3+ years"]}]]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","p",null,{"className":"text-slate-700 mb-4 leading-relaxed","children":"Oversee daily construction activities and ensure quality standards are met."}],["$","div",null,{"className":"mb-6","children":[["$","h4",null,{"className":"font-semibold text-slate-900 mb-2","children":"Requirements:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Diploma in Civil Engineering or related field"]}],["$","li","1",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"3+ years of site supervision experience"]}],["$","li","2",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Strong leadership and communication skills"]}],"$L2f"]}]]}],"$L30"]}]]}]
29:["$","div","2",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm hover:shadow-lg transition-shadow","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl text-slate-900 mb-2","children":"Project Manager"}],["$","div",null,{"className":"flex flex-wrap gap-2 mb-3","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Management"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Full-time"}]]}]]}]}],["$","div",null,{"className":"flex items-center space-x-4 text-sm text-slate-600","children":[["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],"Kathmandu"]}],["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],"7+ years"]}]]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","p",null,{"className":"text-slate-700 mb-4 leading-relaxed","children":"Manage multiple construction projects from planning to completion."}],["$","div",null,{"className":"mb-6","children":[["$","h4",null,{"className":"font-semibold text-slate-900 mb-2","children":"Requirements:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Bachelor's degree in Engineering or Construction Management"]}],["$","li","1",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"7+ years of project management experience"]}],["$","li","2",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"PMP certification preferred"]}],"$L31"]}]]}],"$L32"]}]]}]
2a:["$","div","3",{"data-slot":"card","className":"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm hover:shadow-lg transition-shadow","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"children":[["$","div",null,{"data-slot":"card-title","className":"font-semibold text-xl text-slate-900 mb-2","children":"Architect"}],["$","div",null,{"className":"flex flex-wrap gap-2 mb-3","children":[["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90","children":"Design"}],["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground","children":"Full-time"}]]}]]}]}],["$","div",null,{"className":"flex items-center space-x-4 text-sm text-slate-600","children":[["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-map-pin h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","1r0f0z",{"d":"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0"}],["$","circle","ilqhr7",{"cx":"12","cy":"10","r":"3"}],"$undefined"]}],"Lalitpur"]}],["$","span",null,{"className":"flex items-center","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-clock h-4 w-4 mr-1","aria-hidden":"true","children":[["$","path","mmk7yg",{"d":"M12 6v6l4 2"}],["$","circle","1mglay",{"cx":"12","cy":"12","r":"10"}],"$undefined"]}],"4+ years"]}]]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6","children":[["$","p",null,{"className":"text-slate-700 mb-4 leading-relaxed","children":"Create innovative architectural designs for residential and commercial projects."}],["$","div",null,{"className":"mb-6","children":[["$","h4",null,{"className":"font-semibold text-slate-900 mb-2","children":"Requirements:"}],["$","ul",null,{"className":"space-y-1","children":[["$","li","0",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Bachelor's degree in Architecture"]}],["$","li","1",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"4+ years of architectural design experience"]}],["$","li","2",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Proficiency in CAD and 3D modeling software"]}],"$L33"]}]]}],"$L34"]}]]}]
2f:["$","li","3",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Knowledge of safety protocols"]}]
30:["$","$L11",null,{"href":"/contact","children":["Apply Now",["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right ml-2 h-4 w-4","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-primary-foreground shadow-xs h-9 px-4 py-2 has-[>svg]:px-3 w-full bg-blue-600 hover:bg-blue-700","ref":null}]
31:["$","li","3",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Excellent organizational skills"]}]
32:["$","$L11",null,{"href":"/contact","children":["Apply Now",["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right ml-2 h-4 w-4","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-primary-foreground shadow-xs h-9 px-4 py-2 has-[>svg]:px-3 w-full bg-blue-600 hover:bg-blue-700","ref":null}]
33:["$","li","3",{"className":"text-sm text-slate-600 flex items-start","children":[["$","span",null,{"className":"w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"}],"Creative problem-solving skills"]}]
34:["$","$L11",null,{"href":"/contact","children":["Apply Now",["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right ml-2 h-4 w-4","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive text-primary-foreground shadow-xs h-9 px-4 py-2 has-[>svg]:px-3 w-full bg-blue-600 hover:bg-blue-700","ref":null}]
1c:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
2c:null
35:I[8175,[],"IconMark"]
2e:{"metadata":[["$","title","0",{"children":"Careers - ConstructCo | Construction Jobs Nepal"}],["$","meta","1",{"name":"description","content":"Join ConstructCo's team in Nepal. Explore career opportunities for engineers, architects, project managers, and construction professionals. Build your career with Nepal's leading construction company."}],["$","meta","2",{"name":"keywords","content":"construction jobs nepal, civil engineer jobs, architect careers, construction employment, building industry jobs nepal"}],["$","link","3",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}],["$","$L35","4",{}]],"error":null,"digest":"$undefined"}
1f:"$2e:metadata"
