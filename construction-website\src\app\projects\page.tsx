"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import {
  Home,
  Building,
  Construction,
  MapPin,
  Calendar,
  Users,
  CheckCircle,
  Clock,
  Filter
} from "lucide-react";

const projects = [
  {
    id: 1,
    title: "Sunrise Residential Complex",
    location: "Kathmandu, Nepal",
    type: "Residential",
    status: "Completed",
    year: "2024",
    description: "50-unit modern residential complex with amenities including parking, garden, and community hall.",
    features: ["50 residential units", "Underground parking", "Community garden", "24/7 security"],
    duration: "18 months",
    client: "Sunrise Development Pvt. Ltd.",
    image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 2,
    title: "Tech Hub Office Building",
    location: "Pokhara, Nepal",
    type: "Commercial",
    status: "Completed",
    year: "2023",
    description: "5-story modern office building with retail spaces on ground floor and premium office suites.",
    features: ["5 floors", "Retail spaces", "Premium offices", "Modern elevators"],
    duration: "12 months",
    client: "Tech Hub Nepal",
    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 3,
    title: "Mountain Highway Bridge",
    location: "Chitwan, Nepal",
    type: "Infrastructure",
    status: "Ongoing",
    year: "2024",
    description: "Major bridge construction project connecting rural mountain communities to main highway.",
    features: ["200m span bridge", "Heavy load capacity", "Seismic resistant", "Rural connectivity"],
    duration: "24 months",
    client: "Department of Roads",
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 4,
    title: "Heritage Hotel Renovation",
    location: "Bhaktapur, Nepal",
    type: "Renovation",
    status: "Completed",
    year: "2023",
    description: "Complete renovation of historic hotel while preserving traditional Newari architecture.",
    features: ["Heritage preservation", "Modern amenities", "Traditional design", "Structural upgrade"],
    duration: "8 months",
    client: "Heritage Hotels Nepal",
    image: "https://images.unsplash.com/photo-1581244277943-fe4a9c777189?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 5,
    title: "Green Valley Apartments",
    location: "Lalitpur, Nepal",
    type: "Residential",
    status: "Ongoing",
    year: "2024",
    description: "Eco-friendly apartment complex with solar panels and rainwater harvesting systems.",
    features: ["30 apartments", "Solar power", "Rainwater harvesting", "Green building"],
    duration: "15 months",
    client: "Green Valley Developers",
    image: "https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 6,
    title: "City Mall Extension",
    location: "Kathmandu, Nepal",
    type: "Commercial",
    status: "Completed",
    year: "2022",
    description: "Extension and renovation of existing shopping mall with new retail spaces and food court.",
    features: ["New retail spaces", "Food court", "Parking expansion", "Modern facade"],
    duration: "10 months",
    client: "City Mall Pvt. Ltd.",
    image: "https://images.unsplash.com/photo-1441986300917-64674bd600d8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 7,
    title: "Rural Road Network",
    location: "Gorkha, Nepal",
    type: "Infrastructure",
    status: "Completed",
    year: "2023",
    description: "Construction of 15km rural road network connecting remote villages to district headquarters.",
    features: ["15km road network", "Bridge construction", "Drainage systems", "Village connectivity"],
    duration: "20 months",
    client: "Local Government",
    image: "https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  },
  {
    id: 8,
    title: "Modern Family Home",
    location: "Biratnagar, Nepal",
    type: "Residential",
    status: "Completed",
    year: "2024",
    description: "Custom-designed modern family home with contemporary architecture and smart home features.",
    features: ["Smart home system", "Modern design", "Energy efficient", "Custom interiors"],
    duration: "6 months",
    client: "Private Client",
    image: "https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
  }
];

const filterOptions = [
  { label: "All Projects", value: "all" },
  { label: "Residential", value: "Residential" },
  { label: "Commercial", value: "Commercial" },
  { label: "Infrastructure", value: "Infrastructure" },
  { label: "Renovation", value: "Renovation" }
];

const statusOptions = [
  { label: "All Status", value: "all" },
  { label: "Completed", value: "Completed" },
  { label: "Ongoing", value: "Ongoing" }
];

export default function Projects() {
  const [selectedType, setSelectedType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");

  const filteredProjects = projects.filter(project => {
    const typeMatch = selectedType === "all" || project.type === selectedType;
    const statusMatch = selectedStatus === "all" || project.status === selectedStatus;
    return typeMatch && statusMatch;
  });

  const getProjectIcon = (type: string) => {
    switch (type) {
      case "Residential":
        return Home;
      case "Commercial":
        return Building;
      case "Infrastructure":
        return Construction;
      default:
        return Building;
    }
  };

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <Building className="w-4 h-4 mr-2" />
              Our Projects
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Building Excellence Across{" "}
              <span className="text-orange-400">Nepal</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              Explore our portfolio of successful construction projects spanning residential, 
              commercial, and infrastructure developments throughout Nepal.
            </p>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">150+</div>
              <div className="text-slate-600">Total Projects</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">98%</div>
              <div className="text-slate-600">On-Time Delivery</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">25+</div>
              <div className="text-slate-600">Cities Covered</div>
            </div>
            <div>
              <div className="text-4xl font-bold text-blue-600 mb-2">100%</div>
              <div className="text-slate-600">Client Satisfaction</div>
            </div>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-slate-50 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-slate-600" />
              <span className="font-medium text-slate-700">Filter Projects:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {filterOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedType === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedType(option.value)}
                  className={selectedType === option.value ? "bg-blue-600 hover:bg-blue-700" : ""}
                >
                  {option.label}
                </Button>
              ))}
            </div>
            <div className="flex flex-wrap gap-2">
              {statusOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={selectedStatus === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedStatus(option.value)}
                  className={selectedStatus === option.value ? "bg-orange-600 hover:bg-orange-700" : ""}
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Projects Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <p className="text-slate-600">
              Showing {filteredProjects.length} of {projects.length} projects
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredProjects.map((project) => {
              const ProjectIcon = getProjectIcon(project.type);
              return (
                <Card key={project.id} className="overflow-hidden hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 group">
                  <div className="relative h-64 overflow-hidden">
                    <Image
                      src={project.image}
                      alt={project.title}
                      fill
                      className="object-cover group-hover:scale-110 transition-transform duration-500"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                    <div className="absolute top-4 right-4">
                      <Badge variant={project.status === "Completed" ? "default" : "secondary"} className="bg-white/90 text-slate-900">
                        {project.status === "Completed" ? (
                          <CheckCircle className="w-3 h-3 mr-1" />
                        ) : (
                          <Clock className="w-3 h-3 mr-1" />
                        )}
                        {project.status}
                      </Badge>
                    </div>
                    <div className="absolute top-4 left-4">
                      <Badge variant="outline" className="bg-white/90 border-white/30 text-slate-900">
                        {project.type}
                      </Badge>
                    </div>
                    <div className="absolute bottom-4 left-4 text-white">
                      <p className="text-sm font-medium">{project.location}</p>
                      <p className="text-xs opacity-80">{project.year} • {project.duration}</p>
                    </div>
                  </div>
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold mb-2 text-slate-900 group-hover:text-blue-600 transition-colors">
                      {project.title}
                    </h3>
                    <p className="text-slate-700 text-sm mb-4 leading-relaxed">
                      {project.description}
                    </p>
                    <div className="space-y-2 mb-4">
                      {project.features.slice(0, 3).map((feature, index) => (
                        <div key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-3 w-3 text-green-500 flex-shrink-0" />
                          <span className="text-xs text-slate-600">{feature}</span>
                        </div>
                      ))}
                    </div>
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center text-slate-600">
                        <Users className="h-4 w-4 mr-1" />
                        <span className="text-xs">Client: {project.client}</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Start Your Next Project?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join our satisfied clients and let us bring your construction vision to life.
          </p>
          <Button asChild size="lg" className="bg-orange-500 hover:bg-orange-600">
            <a href="/contact">
              Start Your Project Today
            </a>
          </Button>
        </div>
      </section>
    </div>
  );
}
