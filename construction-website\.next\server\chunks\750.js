exports.id=750,exports.ids=[750],exports.modules={3606:(a,b,c)=>{"use strict";c.d(b,{Header:()=>v});var d=c(60687),e=c(85814),f=c.n(e),g=c(43210),h=c(29523),i=c(97022),j=c(11860),k=c(4780);function l({...a}){return(0,d.jsx)(i.bL,{"data-slot":"sheet",...a})}function m({...a}){return(0,d.jsx)(i.l9,{"data-slot":"sheet-trigger",...a})}function n({...a}){return(0,d.jsx)(i.ZL,{"data-slot":"sheet-portal",...a})}function o({className:a,...b}){return(0,d.jsx)(i.hJ,{"data-slot":"sheet-overlay",className:(0,k.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function p({className:a,children:b,side:c="right",...e}){return(0,d.jsxs)(n,{children:[(0,d.jsx)(o,{}),(0,d.jsxs)(i.UC,{"data-slot":"sheet-content",className:(0,k.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===c&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===c&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===c&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===c&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...e,children:[b,(0,d.jsxs)(i.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,d.jsx)(j.A,{className:"size-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var q=c(48340),r=c(41550),s=c(97992),t=c(12941);let u=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Projects",href:"/projects"},{name:"Gallery",href:"/gallery"},{name:"Contact",href:"/contact"}];function v(){let[a,b]=(0,g.useState)(!1);return(0,d.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/60",children:[(0,d.jsx)("div",{className:"bg-slate-900 text-white py-2",children:(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(q.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:"+977-1-4567890"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(r.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:"<EMAIL>"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)(s.A,{className:"h-3 w-3"}),(0,d.jsx)("span",{children:"Kathmandu, Nepal"})]})]})})}),(0,d.jsx)("div",{className:"container mx-auto px-4",children:(0,d.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,d.jsxs)(f(),{href:"/",className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,d.jsx)("span",{className:"font-bold text-xl text-slate-900",children:"ConstructCo"})]}),(0,d.jsx)("nav",{className:"hidden md:flex items-center space-x-8",children:u.map(a=>(0,d.jsx)(f(),{href:a.href,className:"text-slate-700 hover:text-blue-600 font-medium transition-colors",children:a.name},a.name))}),(0,d.jsx)("div",{className:"hidden md:flex items-center space-x-4",children:(0,d.jsx)(h.$,{asChild:!0,className:"bg-orange-500 hover:bg-orange-600",children:(0,d.jsx)(f(),{href:"/contact",children:"Get Quote"})})}),(0,d.jsxs)(l,{open:a,onOpenChange:b,children:[(0,d.jsx)(m,{asChild:!0,className:"md:hidden",children:(0,d.jsx)(h.$,{variant:"ghost",size:"icon",children:(0,d.jsx)(t.A,{className:"h-6 w-6"})})}),(0,d.jsx)(p,{side:"right",className:"w-[300px] sm:w-[400px]",children:(0,d.jsxs)("nav",{className:"flex flex-col space-y-4 mt-8",children:[u.map(a=>(0,d.jsx)(f(),{href:a.href,className:"text-slate-700 hover:text-blue-600 font-medium transition-colors py-2",onClick:()=>b(!1),children:a.name},a.name)),(0,d.jsx)(h.$,{asChild:!0,className:"bg-orange-500 hover:bg-orange-600 mt-4",children:(0,d.jsx)(f(),{href:"/contact",children:"Get Quote"})})]})})]})]})})]})}},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}},9340:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,85814,23)),Promise.resolve().then(c.bind(c,3606)),Promise.resolve().then(c.bind(c,35950))},10974:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(75986),e=c(8974);function f(...a){return(0,e.QP)((0,d.$)(a))}},12304:(a,b,c)=>{"use strict";c.d(b,{Separator:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\separator.tsx","Separator")},21075:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>x,metadata:()=>w});var d=c(37413),e=c(25091),f=c.n(e);c(61135);var g=c(25045),h=c(4536),i=c.n(h),j=c(23469),k=c(12304),l=c(71750),m=c(60343),n=c(49046),o=c(45868),p=c(63353),q=c(56378),r=c(96262),s=c(65516);let t=[{name:"Home",href:"/"},{name:"About Us",href:"/about"},{name:"Services",href:"/services"},{name:"Projects",href:"/projects"},{name:"Gallery",href:"/gallery"},{name:"Careers",href:"/careers"}],u=[{name:"Residential Construction",href:"/services#residential"},{name:"Commercial Buildings",href:"/services#commercial"},{name:"Infrastructure",href:"/services#infrastructure"},{name:"Renovation",href:"/services#renovation"}];function v(){return(0,d.jsx)("footer",{className:"bg-slate-900 text-white",children:(0,d.jsxs)("div",{className:"container mx-auto px-4 py-12",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("div",{className:"h-8 w-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,d.jsx)("span",{className:"text-white font-bold text-lg",children:"C"})}),(0,d.jsx)("span",{className:"font-bold text-xl",children:"ConstructCo"})]}),(0,d.jsx)("p",{className:"text-slate-300 text-sm leading-relaxed",children:"Building Nepal's future with quality construction, innovative design, and reliable infrastructure solutions for over 10 years."}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(l.A,{className:"h-4 w-4 text-orange-500"}),(0,d.jsx)("span",{children:"+977-1-4567890"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(m.A,{className:"h-4 w-4 text-orange-500"}),(0,d.jsx)("span",{children:"<EMAIL>"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)(n.A,{className:"h-4 w-4 text-orange-500"}),(0,d.jsx)("span",{children:"Kathmandu, Nepal"})]})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg",children:"Quick Links"}),(0,d.jsx)("ul",{className:"space-y-2",children:t.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:a.href,className:"text-slate-300 hover:text-orange-500 transition-colors text-sm",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg",children:"Our Services"}),(0,d.jsx)("ul",{className:"space-y-2",children:u.map(a=>(0,d.jsx)("li",{children:(0,d.jsx)(i(),{href:a.href,className:"text-slate-300 hover:text-orange-500 transition-colors text-sm",children:a.name})},a.name))})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-lg",children:"Connect With Us"}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(j.$,{size:"icon",variant:"outline",className:"border-slate-600 hover:bg-blue-600",children:(0,d.jsx)(o.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{size:"icon",variant:"outline",className:"border-slate-600 hover:bg-blue-400",children:(0,d.jsx)(p.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{size:"icon",variant:"outline",className:"border-slate-600 hover:bg-pink-600",children:(0,d.jsx)(q.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{size:"icon",variant:"outline",className:"border-slate-600 hover:bg-blue-700",children:(0,d.jsx)(r.A,{className:"h-4 w-4"})})]}),(0,d.jsxs)(j.$,{className:"bg-orange-500 hover:bg-orange-600 w-full",children:[(0,d.jsx)(s.A,{className:"h-4 w-4 mr-2"}),"Company Brochure"]})]})]}),(0,d.jsx)(k.Separator,{className:"my-8 bg-slate-700"}),(0,d.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center text-sm text-slate-400",children:[(0,d.jsx)("p",{children:"\xa9 2024 ConstructCo. All rights reserved."}),(0,d.jsxs)("div",{className:"flex space-x-4 mt-4 md:mt-0",children:[(0,d.jsx)(i(),{href:"/faq",className:"hover:text-orange-500 transition-colors",children:"FAQ"}),(0,d.jsx)(i(),{href:"/privacy",className:"hover:text-orange-500 transition-colors",children:"Privacy Policy"}),(0,d.jsx)(i(),{href:"/terms",className:"hover:text-orange-500 transition-colors",children:"Terms of Service"})]})]})]})})}let w={title:"ConstructCo - Building Nepal's Future",description:"Professional construction company in Nepal specializing in residential, commercial, and infrastructure projects. Quality construction with 10+ years of experience.",keywords:"construction, Nepal, building, residential, commercial, infrastructure, renovation"};function x({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} font-sans antialiased`,children:(0,d.jsxs)("div",{className:"flex flex-col min-h-screen",children:[(0,d.jsx)(g.Header,{}),(0,d.jsx)("main",{className:"flex-1",children:a}),(0,d.jsx)(v,{})]})})})}},22908:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,4536,23)),Promise.resolve().then(c.bind(c,25045)),Promise.resolve().then(c.bind(c,12304))},23469:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(37413);c(61120);var e=c(70403),f=c(50662),g=c(10974);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},25045:(a,b,c)=>{"use strict";c.d(b,{Header:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\header.tsx","Header")},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},35950:(a,b,c)=>{"use strict";c.d(b,{Separator:()=>g});var d=c(60687);c(43210);var e=c(62369),f=c(4780);function g({className:a,orientation:b="horizontal",decorative:c=!0,...g}){return(0,d.jsx)(e.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,f.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...g})}},61135:()=>{},62582:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},74206:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))}};