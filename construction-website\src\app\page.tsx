import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  Users,
  Award,
  Clock,
  CheckCircle,
  ArrowRight,
  Star,
  Quote,
  Home as HomeIcon,
  Building,
  Construction,
  Hammer
} from "lucide-react";

const stats = [
  { icon: Building2, label: "Projects Completed", value: "150+" },
  { icon: Users, label: "Happy Clients", value: "200+" },
  { icon: Award, label: "Years Experience", value: "10+" },
  { icon: Clock, label: "On-Time Delivery", value: "98%" },
];

const services = [
  {
    title: "Residential Construction",
    description: "Custom homes, apartments, and residential complexes built to perfection.",
    icon: HomeIcon,
  },
  {
    title: "Commercial Buildings",
    description: "Office buildings, retail spaces, and commercial complexes.",
    icon: Building,
  },
  {
    title: "Infrastructure Projects",
    description: "Roads, bridges, and essential infrastructure development.",
    icon: Construction,
  },
];

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Homeowner",
    content: "ConstructCo built our dream home exactly as we envisioned. Professional, reliable, and excellent quality work.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Business Owner",
    content: "They completed our office building on time and within budget. Highly recommend their services.",
    rating: 5,
  },
];

const featuredProjects = [
  {
    title: "Modern Residential Complex",
    location: "Kathmandu",
    status: "Completed",
    description: "50-unit residential complex with modern amenities",
  },
  {
    title: "Commercial Office Building",
    location: "Pokhara",
    status: "Completed",
    description: "5-story commercial building with retail spaces",
  },
  {
    title: "Highway Bridge Construction",
    location: "Chitwan",
    status: "Ongoing",
    description: "Major infrastructure project connecting rural areas",
  },
];

const trustedPartners = [
  { name: "Nepal Cement Company", category: "Materials" },
  { name: "Himalayan Steel", category: "Steel Supplier" },
  { name: "Modern Architects", category: "Design Partner" },
  { name: "Quality Hardware", category: "Hardware Supplier" },
  { name: "Green Building Materials", category: "Eco Materials" },
  { name: "Nepal Engineering Consultancy", category: "Technical Partner" },
];

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white py-20 lg:py-32">
        {/* Background Pattern */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800"></div>
          <div className="absolute inset-0 opacity-10" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <Hammer className="w-4 h-4 mr-2" />
              Building Nepal&apos;s Future
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Building Tomorrow,{" "}
              <span className="text-orange-400">Today</span>
            </h1>
            <p className="text-xl md:text-2xl mb-8 text-slate-200 leading-relaxed">
              Professional construction services in Nepal with 10+ years of experience.
              From residential homes to commercial buildings and infrastructure projects.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-orange-500 hover:bg-orange-600 text-lg px-8">
                <Link href="/projects">
                  View Our Projects
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="text-lg px-8 border-white text-white hover:bg-white hover:text-slate-900">
                <Link href="/contact">Get Free Quote</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-4">
                  <div className="p-3 bg-blue-100 rounded-full">
                    <stat.icon className="h-8 w-8 text-blue-600" />
                  </div>
                </div>
                <div className="text-3xl font-bold text-slate-900 mb-2">
                  {stat.value}
                </div>
                <div className="text-slate-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Our Construction Services
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              From residential homes to large-scale infrastructure, we deliver quality construction solutions across Nepal.
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-8 text-center">
                  <div className="flex justify-center mb-4">
                    <div className="p-4 bg-blue-100 rounded-full">
                      <service.icon className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold mb-4 text-slate-900">
                    {service.title}
                  </h3>
                  <p className="text-slate-600 mb-6">{service.description}</p>
                  <Button variant="outline" asChild>
                    <Link href="/services">Learn More</Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Featured Projects
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Showcasing our recent construction achievements across Nepal
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {featuredProjects.map((project, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-lg transition-shadow">
                <div className="relative h-48 bg-gradient-to-br from-slate-200 to-slate-300 flex items-center justify-center">
                  <Building2 className="h-16 w-16 text-slate-400" />
                  <div className="absolute top-4 right-4">
                    <Badge variant={project.status === "Completed" ? "default" : "secondary"}>
                      {project.status}
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-slate-900">
                    {project.title}
                  </h3>
                  <p className="text-slate-600 mb-2">{project.location}</p>
                  <p className="text-slate-700 text-sm">{project.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
              <Link href="/projects">
                View All Projects
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
                Why Choose ConstructCo?
              </h2>
              <div className="space-y-4">
                {[
                  "10+ years of construction experience in Nepal",
                  "Licensed and certified construction professionals",
                  "Quality materials from trusted suppliers",
                  "On-time project delivery guarantee",
                  "Comprehensive project management",
                  "Post-construction support and maintenance"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-slate-700">{feature}</span>
                  </div>
                ))}
              </div>
              <Button asChild className="mt-8 bg-blue-600 hover:bg-blue-700">
                <Link href="/about">Learn More About Us</Link>
              </Button>
            </div>
            <div className="relative">
              <div className="bg-slate-100 rounded-lg p-8 h-96 flex items-center justify-center">
                <div className="text-center">
                  <div className="text-6xl font-bold text-blue-600 mb-2">10+</div>
                  <div className="text-xl text-slate-700 mb-4">Years of Excellence</div>
                  <p className="text-slate-600">
                    Building trust through quality construction and reliable service across Nepal.
                  </p>
                </div>
              </div>
              {/* Placeholder for construction image */}
              <div className="absolute inset-0 rounded-lg bg-gradient-to-br from-blue-500/20 to-slate-500/20 flex items-center justify-center">
                <Building2 className="h-24 w-24 text-blue-600/30" />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted Partners */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
              Our Trusted Partners
            </h2>
            <p className="text-lg text-slate-600">
              Working with Nepal&apos;s leading suppliers and consultants
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {trustedPartners.map((partner, index) => (
              <div key={index} className="text-center p-4 hover:bg-slate-50 rounded-lg transition-colors">
                <div className="w-16 h-16 bg-slate-200 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Building2 className="h-8 w-8 text-slate-400" />
                </div>
                <h4 className="font-medium text-slate-900 text-sm mb-1">{partner.name}</h4>
                <p className="text-xs text-slate-600">{partner.category}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 bg-slate-900 text-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              What Our Clients Say
            </h2>
            <p className="text-xl text-slate-300">
              Trusted by homeowners and businesses across Nepal
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-slate-800 border-slate-700">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-orange-500 mb-4" />
                  <p className="text-slate-200 mb-6 text-lg leading-relaxed">
                    "                    &ldquo;{testimonial.content}&rdquo;"
                  </p>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-white">{testimonial.name}</div>
                      <div className="text-slate-400">{testimonial.role}</div>
                    </div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-orange-500 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Start Your Construction Project?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Get a free consultation and quote for your residential, commercial, or infrastructure project.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-white text-orange-500 hover:bg-slate-100">
              <Link href="/contact">Get Free Quote</Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-orange-500">
              <Link href="/projects">View Our Work</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
