import Link from "next/link";
import Image from "next/image";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Building2,
  Users,
  Award,
  Clock,
  CheckCircle,
  ArrowRight,
  Star,
  Quote,
  Home as HomeIcon,
  Building,
  Construction,
  Hammer,
  Shield
} from "lucide-react";

const stats = [
  { icon: Building2, label: "Projects Completed", value: "150+" },
  { icon: Users, label: "Happy Clients", value: "200+" },
  { icon: Award, label: "Years Experience", value: "10+" },
  { icon: Clock, label: "On-Time Delivery", value: "98%" },
];

const services = [
  {
    title: "Residential Construction",
    description: "Custom homes, apartments, and residential complexes built to perfection.",
    icon: HomeIcon,
  },
  {
    title: "Commercial Buildings",
    description: "Office buildings, retail spaces, and commercial complexes.",
    icon: Building,
  },
  {
    title: "Infrastructure Projects",
    description: "Roads, bridges, and essential infrastructure development.",
    icon: Construction,
  },
];

const testimonials = [
  {
    name: "<PERSON><PERSON>",
    role: "Homeowner",
    content: "ConstructCo built our dream home exactly as we envisioned. Professional, reliable, and excellent quality work.",
    rating: 5,
  },
  {
    name: "<PERSON>",
    role: "Business Owner",
    content: "They completed our office building on time and within budget. Highly recommend their services.",
    rating: 5,
  },
];

const featuredProjects = [
  {
    title: "Modern Residential Complex",
    location: "Kathmandu",
    status: "Completed",
    description: "50-unit residential complex with modern amenities",
    image: "https://images.unsplash.com/photo-1545324418-cc1a3fa10c00?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
  },
  {
    title: "Commercial Office Building",
    location: "Pokhara",
    status: "Completed",
    description: "5-story commercial building with retail spaces",
    image: "https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
  },
  {
    title: "Highway Bridge Construction",
    location: "Chitwan",
    status: "Ongoing",
    description: "Major infrastructure project connecting rural areas",
    image: "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80",
  },
];

const trustedPartners = [
  { name: "Nepal Cement Company", category: "Materials" },
  { name: "Himalayan Steel", category: "Steel Supplier" },
  { name: "Modern Architects", category: "Design Partner" },
  { name: "Quality Hardware", category: "Hardware Supplier" },
  { name: "Green Building Materials", category: "Eco Materials" },
  { name: "Nepal Engineering Consultancy", category: "Technical Partner" },
];

export default function Home() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative min-h-screen flex items-center bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white overflow-hidden">
        {/* Animated Background Elements */}
        <div className="absolute inset-0">
          {/* Primary gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800"></div>

          {/* Geometric shapes */}
          <div className="absolute top-20 left-10 w-32 h-32 bg-orange-500/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-20 right-10 w-48 h-48 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-white/5 rotate-45 animate-bounce delay-500"></div>

          {/* Grid pattern */}
          <div className="absolute inset-0 opacity-5" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.3'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>

          {/* Overlay gradient */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="max-w-5xl mx-auto">
            <div className="grid lg:grid-cols-2 gap-12 items-center">
              {/* Left Content */}
              <div className="text-center lg:text-left">
                <div className="inline-flex items-center px-4 py-2 bg-orange-500/20 border border-orange-500/30 rounded-full mb-8 backdrop-blur-sm">
                  <Hammer className="w-5 h-5 mr-3 text-orange-400" />
                  <span className="text-orange-300 font-medium">Building Nepal&apos;s Future Since 2014</span>
                </div>

                <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
                  Building{" "}
                  <span className="bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
                    Tomorrow
                  </span>
                  <br />
                  <span className="text-4xl md:text-5xl text-slate-300">Today</span>
                </h1>

                <p className="text-xl md:text-2xl mb-8 text-slate-300 leading-relaxed max-w-2xl mx-auto lg:mx-0">
                  Transform your vision into reality with Nepal&apos;s most trusted construction company.
                  <span className="text-orange-300 font-semibold">10+ years</span> of excellence in residential, commercial, and infrastructure projects.
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start mb-8">
                  <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-lg px-8 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                    <Link href="/projects">
                      <Building2 className="mr-2 h-5 w-5" />
                      View Our Projects
                      <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                  <Button asChild size="lg" variant="outline" className="text-lg px-8 py-4 border-2 border-white/30 text-white hover:bg-white hover:text-slate-900 backdrop-blur-sm transition-all duration-300">
                    <Link href="/contact">
                      <CheckCircle className="mr-2 h-5 w-5" />
                      Get Free Quote
                    </Link>
                  </Button>
                </div>

                {/* Quick Stats */}
                <div className="flex flex-wrap gap-6 justify-center lg:justify-start text-sm">
                  <div className="flex items-center text-slate-300">
                    <Award className="w-4 h-4 mr-2 text-orange-400" />
                    <span>150+ Projects</span>
                  </div>
                  <div className="flex items-center text-slate-300">
                    <Users className="w-4 h-4 mr-2 text-orange-400" />
                    <span>200+ Happy Clients</span>
                  </div>
                  <div className="flex items-center text-slate-300">
                    <Clock className="w-4 h-4 mr-2 text-orange-400" />
                    <span>98% On-Time</span>
                  </div>
                </div>
              </div>

              {/* Right Visual Element */}
              <div className="relative lg:block hidden">
                <div className="relative">
                  {/* Main construction image */}
                  <div className="w-96 h-96 mx-auto relative">
                    {/* Primary image */}
                    <div className="relative w-full h-full rounded-2xl overflow-hidden shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-500">
                      <Image
                        src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                        alt="Construction site with modern building"
                        fill
                        className="object-cover"
                        priority
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"></div>
                      <div className="absolute bottom-4 left-4 text-white">
                        <p className="text-sm font-medium">Modern Construction</p>
                        <p className="text-xs opacity-80">Kathmandu Project</p>
                      </div>
                    </div>

                    {/* Secondary floating image */}
                    <div className="absolute -bottom-8 -left-8 w-32 h-24 rounded-xl overflow-hidden shadow-xl border-4 border-white transform -rotate-6 hover:rotate-0 transition-transform duration-500">
                      <Image
                        src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Construction workers"
                        fill
                        className="object-cover"
                      />
                    </div>

                    {/* Third floating image */}
                    <div className="absolute -top-6 -right-6 w-28 h-20 rounded-lg overflow-hidden shadow-xl border-4 border-white transform rotate-12 hover:rotate-6 transition-transform duration-500">
                      <Image
                        src="https://images.unsplash.com/photo-1590736969955-71cc94901144?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        alt="Building architecture"
                        fill
                        className="object-cover"
                      />
                    </div>

                    {/* Floating elements */}
                    <div className="absolute -top-8 -right-8 w-16 h-16 bg-orange-500 rounded-full flex items-center justify-center shadow-lg animate-bounce z-10">
                      <Hammer className="w-8 h-8 text-white" />
                    </div>
                    <div className="absolute -bottom-4 -left-4 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center shadow-lg animate-pulse z-10">
                      <Building2 className="w-6 h-6 text-white" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-white/30 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-white/50 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-slate-50 to-blue-50 relative overflow-hidden">
        {/* Background decoration */}
        <div className="absolute top-0 left-0 w-full h-2 bg-gradient-to-r from-orange-500 to-yellow-500"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Proven Track Record
            </h2>
            <p className="text-lg text-slate-600 max-w-2xl mx-auto">
              Numbers that speak for our commitment to excellence and client satisfaction
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center group">
                <div className="relative mb-6">
                  <div className="flex justify-center">
                    <div className="relative p-6 bg-white rounded-2xl shadow-lg group-hover:shadow-xl transition-all duration-300 transform group-hover:scale-105">
                      <div className="absolute inset-0 bg-gradient-to-br from-blue-500 to-orange-500 rounded-2xl opacity-0 group-hover:opacity-10 transition-opacity duration-300"></div>
                      <stat.icon className="h-10 w-10 text-blue-600 group-hover:text-orange-600 transition-colors duration-300 relative z-10" />
                    </div>
                  </div>
                  {/* Floating decoration */}
                  <div className="absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
                </div>
                <div className="text-4xl md:text-5xl font-bold text-slate-900 mb-2 group-hover:text-blue-600 transition-colors duration-300">
                  {stat.value}
                </div>
                <div className="text-slate-600 font-medium">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Services Preview */}
      <section className="py-20 bg-white relative">
        {/* Background decoration */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-orange-50/50"></div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-blue-100 text-blue-800 hover:bg-blue-200">
              <Building2 className="w-4 h-4 mr-2" />
              Our Expertise
            </Badge>
            <h2 className="text-3xl md:text-5xl font-bold text-slate-900 mb-6">
              Construction Services That{" "}
              <span className="bg-gradient-to-r from-blue-600 to-orange-600 bg-clip-text text-transparent">
                Build Dreams
              </span>
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto leading-relaxed">
              From residential homes to large-scale infrastructure, we deliver quality construction solutions
              that stand the test of time across Nepal.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-8">
            {services.map((service, index) => (
              <Card key={index} className="group hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 border-0 bg-white/80 backdrop-blur-sm">
                <CardContent className="p-8 text-center relative overflow-hidden">
                  {/* Background gradient on hover */}
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-orange-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

                  <div className="relative z-10">
                    <div className="flex justify-center mb-6">
                      <div className="relative">
                        <div className="p-6 bg-gradient-to-br from-blue-100 to-orange-100 rounded-2xl group-hover:from-blue-500 group-hover:to-orange-500 transition-all duration-500 transform group-hover:scale-110">
                          <service.icon className="h-10 w-10 text-blue-600 group-hover:text-white transition-colors duration-500" />
                        </div>
                        {/* Floating ring */}
                        <div className="absolute inset-0 border-2 border-blue-200 rounded-2xl animate-pulse opacity-0 group-hover:opacity-100 transition-opacity duration-500 scale-125"></div>
                      </div>
                    </div>

                    <h3 className="text-xl font-bold mb-4 text-slate-900 group-hover:text-blue-600 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-slate-600 mb-6 leading-relaxed">{service.description}</p>

                    <Button variant="outline" asChild className="group-hover:bg-blue-600 group-hover:text-white group-hover:border-blue-600 transition-all duration-300">
                      <Link href="/services">
                        Learn More
                        <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Projects */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Featured Projects
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Showcasing our recent construction achievements across Nepal
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            {featuredProjects.map((project, index) => (
              <Card key={index} className="overflow-hidden hover:shadow-xl transition-all duration-500 transform hover:-translate-y-2 group">
                <div className="relative h-64 overflow-hidden">
                  <Image
                    src={project.image}
                    alt={project.title}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>
                  <div className="absolute top-4 right-4">
                    <Badge variant={project.status === "Completed" ? "default" : "secondary"} className="bg-white/90 text-slate-900">
                      {project.status}
                    </Badge>
                  </div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <p className="text-sm font-medium opacity-90">{project.location}</p>
                  </div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-2 text-slate-900 group-hover:text-blue-600 transition-colors">
                    {project.title}
                  </h3>
                  <p className="text-slate-700 text-sm leading-relaxed">{project.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
          <div className="text-center mt-12">
            <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
              <Link href="/projects">
                View All Projects
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Why Choose Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-6">
                Why Choose ConstructCo?
              </h2>
              <div className="space-y-4">
                {[
                  "10+ years of construction experience in Nepal",
                  "Licensed and certified construction professionals",
                  "Quality materials from trusted suppliers",
                  "On-time project delivery guarantee",
                  "Comprehensive project management",
                  "Post-construction support and maintenance"
                ].map((feature, index) => (
                  <div key={index} className="flex items-center space-x-3">
                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0" />
                    <span className="text-slate-700">{feature}</span>
                  </div>
                ))}
              </div>
              <Button asChild className="mt-8 bg-blue-600 hover:bg-blue-700">
                <Link href="/about">Learn More About Us</Link>
              </Button>
            </div>
            <div className="relative">
              <div className="relative h-96 rounded-2xl overflow-hidden shadow-2xl">
                <Image
                  src="https://images.unsplash.com/photo-1503387762-592deb58ef4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
                  alt="Construction team working on site"
                  fill
                  className="object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/30 to-transparent"></div>
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-center text-white">
                    <div className="text-6xl font-bold mb-2 drop-shadow-lg">10+</div>
                    <div className="text-xl mb-4 drop-shadow-md">Years of Excellence</div>
                    <p className="text-slate-200 max-w-xs mx-auto drop-shadow-md">
                      Building trust through quality construction and reliable service across Nepal.
                    </p>
                  </div>
                </div>
                {/* Decorative elements */}
                <div className="absolute top-4 right-4 w-12 h-12 bg-orange-500/20 rounded-full backdrop-blur-sm border border-orange-500/30 flex items-center justify-center">
                  <Award className="w-6 h-6 text-orange-400" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Trusted Partners */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-2xl md:text-3xl font-bold text-slate-900 mb-4">
              Our Trusted Partners
            </h2>
            <p className="text-lg text-slate-600">
              Working with Nepal&apos;s leading suppliers and consultants
            </p>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {trustedPartners.map((partner, index) => (
              <div key={index} className="text-center p-4 hover:bg-slate-50 rounded-lg transition-colors">
                <div className="w-16 h-16 bg-slate-200 rounded-lg flex items-center justify-center mx-auto mb-3">
                  <Building2 className="h-8 w-8 text-slate-400" />
                </div>
                <h4 className="font-medium text-slate-900 text-sm mb-1">{partner.name}</h4>
                <p className="text-xs text-slate-600">{partner.category}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials */}
      <section className="py-20 relative overflow-hidden">
        {/* Background image with overlay */}
        <div className="absolute inset-0">
          <Image
            src="https://images.unsplash.com/photo-1541976590-713941681591?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80"
            alt="Construction site background"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-slate-900/85"></div>
        </div>

        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center mb-16">
            <Badge className="mb-4 bg-orange-500/20 border border-orange-500/30 text-orange-300">
              <Star className="w-4 h-4 mr-2" />
              Client Testimonials
            </Badge>
            <h2 className="text-3xl md:text-4xl font-bold mb-4 text-white">
              What Our Clients Say
            </h2>
            <p className="text-xl text-slate-300">
              Trusted by homeowners and businesses across Nepal
            </p>
          </div>
          <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
            {testimonials.map((testimonial, index) => (
              <Card key={index} className="bg-white/10 backdrop-blur-md border-white/20 hover:bg-white/15 transition-all duration-300 transform hover:scale-105">
                <CardContent className="p-8">
                  <Quote className="h-8 w-8 text-orange-400 mb-4" />
                  <p className="text-white mb-6 text-lg leading-relaxed">
                    &ldquo;{testimonial.content}&rdquo;
                  </p>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-white">{testimonial.name}</div>
                      <div className="text-slate-300">{testimonial.role}</div>
                    </div>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 text-white relative overflow-hidden">
        {/* Background elements */}
        <div className="absolute inset-0">
          <div className="absolute top-10 left-10 w-32 h-32 bg-orange-500/10 rounded-full blur-xl animate-pulse"></div>
          <div className="absolute bottom-10 right-10 w-40 h-40 bg-blue-500/10 rounded-full blur-xl animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-orange-500/5 to-blue-500/5 rounded-full blur-3xl"></div>
        </div>

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <Badge className="mb-6 bg-orange-500/20 border border-orange-500/30 text-orange-300">
              <CheckCircle className="w-4 h-4 mr-2" />
              Ready to Build?
            </Badge>

            <h2 className="text-4xl md:text-6xl font-bold mb-6 leading-tight">
              Let&apos;s Build Your{" "}
              <span className="bg-gradient-to-r from-orange-400 to-yellow-400 bg-clip-text text-transparent">
                Dream Project
              </span>
            </h2>

            <p className="text-xl md:text-2xl mb-12 text-slate-300 leading-relaxed max-w-3xl mx-auto">
              Transform your vision into reality with Nepal&apos;s most trusted construction experts.
              Get a <span className="text-orange-400 font-semibold">free consultation</span> and detailed quote today.
            </p>

            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-12">
              <Button asChild size="lg" className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-lg px-10 py-4 shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-105">
                <Link href="/contact">
                  <CheckCircle className="mr-2 h-5 w-5" />
                  Get Free Quote
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
              <Button asChild size="lg" variant="outline" className="text-lg px-10 py-4 border-2 border-white/30 text-white hover:bg-white hover:text-slate-900 backdrop-blur-sm transition-all duration-300">
                <Link href="/projects">
                  <Building2 className="mr-2 h-5 w-5" />
                  View Our Work
                </Link>
              </Button>
            </div>

            {/* Trust indicators */}
            <div className="flex flex-wrap gap-8 justify-center items-center text-sm text-slate-400">
              <div className="flex items-center">
                <Award className="w-4 h-4 mr-2 text-orange-400" />
                <span>Licensed & Certified</span>
              </div>
              <div className="flex items-center">
                <Shield className="w-4 h-4 mr-2 text-orange-400" />
                <span>Fully Insured</span>
              </div>
              <div className="flex items-center">
                <Clock className="w-4 h-4 mr-2 text-orange-400" />
                <span>24/7 Support</span>
              </div>
              <div className="flex items-center">
                <CheckCircle className="w-4 h-4 mr-2 text-orange-400" />
                <span>Quality Guaranteed</span>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
