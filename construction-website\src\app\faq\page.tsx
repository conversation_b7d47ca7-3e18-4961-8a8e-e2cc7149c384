"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  ChevronDown,
  ChevronUp,
  HelpCircle,
  Building,
  Clock,
  DollarSign,
  FileText,
  Shield,
  Users,
  ArrowRight
} from "lucide-react";

const faqCategories = [
  {
    id: "general",
    title: "General Questions",
    icon: HelpCircle,
    faqs: [
      {
        question: "What types of construction projects do you handle?",
        answer: "We handle a wide range of construction projects including residential homes, commercial buildings, infrastructure projects like roads and bridges, renovations, and interior design. Our expertise spans from small residential projects to large-scale commercial and infrastructure developments."
      },
      {
        question: "In which areas of Nepal do you operate?",
        answer: "We operate throughout Nepal with our head office in Kathmandu. We have successfully completed projects in major cities including Kathmandu, Pokhara, Lalitpur, Bhaktapur, Biratnagar, and many rural areas. Our team can travel to any location within Nepal for project execution."
      },
      {
        question: "How long has ConstructCo been in business?",
        answer: "ConstructCo has been serving Nepal's construction industry for over 10 years since our establishment in 2014. During this time, we have completed 150+ projects and built a reputation for quality, reliability, and professional service."
      }
    ]
  },
  {
    id: "pricing",
    title: "Pricing & Budget",
    icon: DollarSign,
    faqs: [
      {
        question: "How do you determine project costs?",
        answer: "Project costs are determined based on several factors including project size, complexity, materials required, location, timeline, and specific client requirements. We provide detailed cost estimates after an initial consultation and site assessment."
      },
      {
        question: "Do you provide free estimates?",
        answer: "Yes, we provide free initial consultations and project estimates. Our team will visit your site, understand your requirements, and provide a detailed quote within 48-72 hours at no cost to you."
      },
      {
        question: "What payment methods do you accept?",
        answer: "We accept various payment methods including bank transfers, checks, and cash payments. Payment is typically structured in phases based on project milestones, with an initial advance payment to begin work."
      },
      {
        question: "Are there any hidden costs?",
        answer: "No, we believe in transparent pricing. All costs are clearly outlined in our detailed estimates. Any additional costs due to scope changes or unforeseen circumstances are discussed and approved with clients before proceeding."
      }
    ]
  },
  {
    id: "timeline",
    title: "Timeline & Process",
    icon: Clock,
    faqs: [
      {
        question: "How long does a typical construction project take?",
        answer: "Project timelines vary based on scope and complexity. Residential homes typically take 3-8 months, commercial buildings 6-18 months, and infrastructure projects 12-36 months. We provide detailed project schedules during the planning phase."
      },
      {
        question: "What is your construction process?",
        answer: "Our process includes: 1) Initial consultation and site assessment, 2) Design and planning phase, 3) Permit acquisition and approvals, 4) Material procurement, 5) Construction execution, 6) Quality inspections, 7) Project handover and warranty support."
      },
      {
        question: "How do you handle project delays?",
        answer: "We plan projects carefully to minimize delays. However, if delays occur due to weather, permit issues, or unforeseen circumstances, we communicate immediately with clients and adjust timelines accordingly while maintaining quality standards."
      }
    ]
  },
  {
    id: "permits",
    title: "Permits & Legal",
    icon: FileText,
    faqs: [
      {
        question: "Do you help with building permits and approvals?",
        answer: "Yes, we assist clients with all necessary permits and approvals including building permits, environmental clearances, and municipal approvals. Our team is familiar with local regulations and can navigate the approval process efficiently."
      },
      {
        question: "Are you licensed and insured?",
        answer: "Yes, ConstructCo is fully licensed by the Department of Urban Development and Building Construction, Nepal. We carry comprehensive insurance coverage including liability insurance and worker's compensation."
      },
      {
        question: "What building codes do you follow?",
        answer: "We strictly follow Nepal National Building Code (NBC) and all relevant local building regulations. Our designs and construction practices comply with seismic safety requirements and environmental standards."
      }
    ]
  },
  {
    id: "quality",
    title: "Quality & Warranty",
    icon: Shield,
    faqs: [
      {
        question: "What quality assurance measures do you have?",
        answer: "We have rigorous quality control processes including material testing, regular inspections at each construction phase, adherence to engineering specifications, and final quality audits before project handover."
      },
      {
        question: "Do you provide warranties on your work?",
        answer: "Yes, we provide comprehensive warranties: 10-20 years for structural work, 5 years for general construction work, and 1-3 years for finishes and fixtures. Warranty terms vary based on project type and components."
      },
      {
        question: "What materials do you use?",
        answer: "We use only high-quality materials from trusted suppliers. All materials meet or exceed industry standards and building code requirements. We can accommodate client preferences for specific brands or eco-friendly materials."
      }
    ]
  },
  {
    id: "communication",
    title: "Communication & Support",
    icon: Users,
    faqs: [
      {
        question: "How do you keep clients updated on project progress?",
        answer: "We provide regular progress updates through weekly reports, photos, and scheduled site visits. Clients can also visit the construction site anytime with prior coordination with our site supervisor."
      },
      {
        question: "Who will be my main point of contact?",
        answer: "Each project is assigned a dedicated project manager who serves as your primary contact throughout the construction process. You'll also have direct access to our site supervisor and technical team as needed."
      },
      {
        question: "Do you provide post-construction support?",
        answer: "Yes, we provide comprehensive post-construction support including warranty service, maintenance guidance, and assistance with any issues that may arise after project completion."
      }
    ]
  }
];

export default function FAQ() {
  const [activeCategory, setActiveCategory] = useState("general");
  const [openFAQ, setOpenFAQ] = useState<string | null>(null);

  const toggleFAQ = (categoryId: string, faqIndex: number) => {
    const faqId = `${categoryId}-${faqIndex}`;
    setOpenFAQ(openFAQ === faqId ? null : faqId);
  };

  const activeCategoryData = faqCategories.find(cat => cat.id === activeCategory);

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <HelpCircle className="w-4 h-4 mr-2" />
              Frequently Asked Questions
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Get Answers to Your{" "}
              <span className="text-orange-400">Construction Questions</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              Find answers to common questions about our construction services, processes, 
              pricing, and more. Can't find what you're looking for? Contact us directly.
            </p>
          </div>
        </div>
      </section>

      {/* FAQ Content */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-4 gap-8">
            {/* Category Navigation */}
            <div className="lg:col-span-1">
              <div className="sticky top-8">
                <h3 className="text-lg font-semibold text-slate-900 mb-4">Categories</h3>
                <div className="space-y-2">
                  {faqCategories.map((category) => (
                    <Button
                      key={category.id}
                      variant={activeCategory === category.id ? "default" : "ghost"}
                      className={`w-full justify-start ${
                        activeCategory === category.id 
                          ? "bg-blue-600 hover:bg-blue-700 text-white" 
                          : "text-slate-700 hover:text-blue-600"
                      }`}
                      onClick={() => setActiveCategory(category.id)}
                    >
                      <category.icon className="w-4 h-4 mr-2" />
                      {category.title}
                    </Button>
                  ))}
                </div>
              </div>
            </div>

            {/* FAQ Content */}
            <div className="lg:col-span-3">
              {activeCategoryData && (
                <div>
                  <div className="flex items-center mb-8">
                    <activeCategoryData.icon className="w-6 h-6 text-blue-600 mr-3" />
                    <h2 className="text-2xl font-bold text-slate-900">
                      {activeCategoryData.title}
                    </h2>
                  </div>
                  
                  <div className="space-y-4">
                    {activeCategoryData.faqs.map((faq, index) => {
                      const faqId = `${activeCategory}-${index}`;
                      const isOpen = openFAQ === faqId;
                      
                      return (
                        <Card key={index} className="overflow-hidden">
                          <CardContent className="p-0">
                            <button
                              className="w-full p-6 text-left hover:bg-slate-50 transition-colors"
                              onClick={() => toggleFAQ(activeCategory, index)}
                            >
                              <div className="flex items-center justify-between">
                                <h3 className="text-lg font-semibold text-slate-900 pr-4">
                                  {faq.question}
                                </h3>
                                {isOpen ? (
                                  <ChevronUp className="w-5 h-5 text-slate-500 flex-shrink-0" />
                                ) : (
                                  <ChevronDown className="w-5 h-5 text-slate-500 flex-shrink-0" />
                                )}
                              </div>
                            </button>
                            {isOpen && (
                              <div className="px-6 pb-6">
                                <p className="text-slate-700 leading-relaxed">
                                  {faq.answer}
                                </p>
                              </div>
                            )}
                          </CardContent>
                        </Card>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>

      {/* Contact CTA */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-2xl mx-auto">
            <h2 className="text-3xl font-bold text-slate-900 mb-4">
              Still Have Questions?
            </h2>
            <p className="text-xl text-slate-600 mb-8">
              Our team is here to help. Get in touch for personalized answers to your construction questions.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" className="bg-blue-600 hover:bg-blue-700">
                <a href="/contact">
                  Contact Us
                  <ArrowRight className="ml-2 h-5 w-5" />
                </a>
              </Button>
              <Button asChild size="lg" variant="outline">
                <a href="tel:+977-1-4567890">
                  Call: +977-1-4567890
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
