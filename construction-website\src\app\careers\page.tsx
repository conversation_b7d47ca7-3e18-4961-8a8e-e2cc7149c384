import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Careers - ConstructCo | Construction Jobs Nepal",
  description: "<PERSON><PERSON> ConstructCo's team in Nepal. Explore career opportunities for engineers, architects, project managers, and construction professionals. Build your career with Nepal's leading construction company.",
  keywords: "construction jobs nepal, civil engineer jobs, architect careers, construction employment, building industry jobs nepal",
};
import {
  Briefcase,
  MapPin,
  Clock,
  Users,
  GraduationCap,
  Award,
  Heart,
  Shield,
  TrendingUp,
  Coffee,
  ArrowRight
} from "lucide-react";

const openPositions = [
  {
    title: "Senior Civil Engineer",
    department: "Engineering",
    location: "Kathmandu",
    type: "Full-time",
    experience: "5+ years",
    description: "Lead structural design and project management for large-scale construction projects.",
    requirements: [
      "Bachelor's degree in Civil Engineering",
      "5+ years of construction experience",
      "Project management certification preferred",
      "Strong knowledge of building codes"
    ]
  },
  {
    title: "Site Supervisor",
    department: "Operations",
    location: "Pokhara",
    type: "Full-time", 
    experience: "3+ years",
    description: "Oversee daily construction activities and ensure quality standards are met.",
    requirements: [
      "Diploma in Civil Engineering or related field",
      "3+ years of site supervision experience",
      "Strong leadership and communication skills",
      "Knowledge of safety protocols"
    ]
  },
  {
    title: "Project Manager",
    department: "Management",
    location: "Kathmandu",
    type: "Full-time",
    experience: "7+ years",
    description: "Manage multiple construction projects from planning to completion.",
    requirements: [
      "Bachelor's degree in Engineering or Construction Management",
      "7+ years of project management experience",
      "PMP certification preferred",
      "Excellent organizational skills"
    ]
  },
  {
    title: "Architect",
    department: "Design",
    location: "Lalitpur",
    type: "Full-time",
    experience: "4+ years",
    description: "Create innovative architectural designs for residential and commercial projects.",
    requirements: [
      "Bachelor's degree in Architecture",
      "4+ years of architectural design experience",
      "Proficiency in CAD and 3D modeling software",
      "Creative problem-solving skills"
    ]
  }
];

const benefits = [
  {
    icon: Award,
    title: "Competitive Salary",
    description: "Market-leading compensation packages with performance bonuses"
  },
  {
    icon: Heart,
    title: "Health Insurance",
    description: "Comprehensive health coverage for you and your family"
  },
  {
    icon: GraduationCap,
    title: "Professional Development",
    description: "Training programs and certification support for career growth"
  },
  {
    icon: Coffee,
    title: "Work-Life Balance",
    description: "Flexible working hours and paid time off"
  },
  {
    icon: TrendingUp,
    title: "Career Growth",
    description: "Clear advancement paths and leadership opportunities"
  },
  {
    icon: Shield,
    title: "Job Security",
    description: "Stable employment with a growing construction company"
  }
];

const companyValues = [
  {
    title: "Innovation",
    description: "We embrace new technologies and construction methods"
  },
  {
    title: "Quality",
    description: "Excellence in every project we undertake"
  },
  {
    title: "Teamwork",
    description: "Collaborative environment where everyone contributes"
  },
  {
    title: "Growth",
    description: "Continuous learning and professional development"
  }
];

export default function Careers() {
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <Briefcase className="w-4 h-4 mr-2" />
              Join Our Team
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Build Your Career with{" "}
              <span className="text-orange-400">ConstructCo</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              Join Nepal's leading construction company and be part of building the nation's future. 
              We offer exciting opportunities for growth and professional development.
            </p>
          </div>
        </div>
      </section>

      {/* Why Work With Us */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Why Choose ConstructCo?
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              We're not just building structures; we're building careers and futures
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {companyValues.map((value, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <h3 className="text-xl font-semibold mb-3 text-slate-900">
                    {value.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Employee Benefits
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              We take care of our team with comprehensive benefits and support
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="flex items-center mb-4">
                    <div className="p-3 bg-blue-100 rounded-full mr-4">
                      <benefit.icon className="h-6 w-6 text-blue-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-slate-900">
                      {benefit.title}
                    </h3>
                  </div>
                  <p className="text-slate-600 leading-relaxed">
                    {benefit.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Open Positions */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Current Openings
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Explore exciting career opportunities across different departments
            </p>
          </div>
          <div className="grid lg:grid-cols-2 gap-8">
            {openPositions.map((position, index) => (
              <Card key={index} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-start justify-between">
                    <div>
                      <CardTitle className="text-xl text-slate-900 mb-2">
                        {position.title}
                      </CardTitle>
                      <div className="flex flex-wrap gap-2 mb-3">
                        <Badge variant="secondary">{position.department}</Badge>
                        <Badge variant="outline">{position.type}</Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-slate-600">
                    <span className="flex items-center">
                      <MapPin className="h-4 w-4 mr-1" />
                      {position.location}
                    </span>
                    <span className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {position.experience}
                    </span>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="text-slate-700 mb-4 leading-relaxed">
                    {position.description}
                  </p>
                  <div className="mb-6">
                    <h4 className="font-semibold text-slate-900 mb-2">Requirements:</h4>
                    <ul className="space-y-1">
                      {position.requirements.map((req, reqIndex) => (
                        <li key={reqIndex} className="text-sm text-slate-600 flex items-start">
                          <span className="w-1.5 h-1.5 bg-blue-600 rounded-full mt-2 mr-2 flex-shrink-0"></span>
                          {req}
                        </li>
                      ))}
                    </ul>
                  </div>
                  <Button asChild className="w-full bg-blue-600 hover:bg-blue-700">
                    <Link href="/contact">
                      Apply Now
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Application Process */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              Application Process
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Simple and transparent hiring process
            </p>
          </div>
          <div className="grid md:grid-cols-4 gap-8">
            {[
              {
                step: "01",
                title: "Apply Online",
                description: "Submit your application through our contact form"
              },
              {
                step: "02",
                title: "Initial Review",
                description: "Our HR team reviews your application and qualifications"
              },
              {
                step: "03",
                title: "Interview",
                description: "Technical and cultural fit interviews with our team"
              },
              {
                step: "04",
                title: "Welcome Aboard",
                description: "Onboarding and orientation to start your journey"
              }
            ].map((step, index) => (
              <Card key={index} className="text-center p-6">
                <CardContent className="p-0">
                  <div className="w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg mx-auto mb-4">
                    {step.step}
                  </div>
                  <h3 className="text-lg font-semibold mb-3 text-slate-900">
                    {step.title}
                  </h3>
                  <p className="text-slate-600 text-sm leading-relaxed">
                    {step.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Join Our Team?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Take the next step in your construction career with ConstructCo.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-orange-500 hover:bg-orange-600">
              <Link href="/contact">
                Apply Now
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
            <Button asChild size="lg" variant="outline" className="border-white text-white hover:bg-white hover:text-blue-600">
              <Link href="/about">Learn More About Us</Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  );
}
