module.exports = {

"[project]/src/app/gallery/layout.tsx [app-rsc] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "default": ()=>GalleryLayout,
    "metadata": ()=>metadata
});
const metadata = {
    title: "Gallery - ConstructCo | Construction Project Photos Nepal",
    description: "View our construction project gallery showcasing quality workmanship across residential, commercial, and infrastructure projects in Nepal. Visual journey of construction excellence.",
    keywords: "construction gallery nepal, project photos, building images, construction work gallery, before after construction"
};
function GalleryLayout({ children }) {
    return children;
}
}),

};

//# sourceMappingURL=src_app_gallery_layout_tsx_597503ff._.js.map