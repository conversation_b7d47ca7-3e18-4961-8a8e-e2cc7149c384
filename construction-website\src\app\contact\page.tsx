"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import {
  Phone,
  Mail,
  MapPin,
  Clock,
  Send,
  MessageSquare,
  Building,
  User,
  Calendar,
  CheckCircle
} from "lucide-react";

const contactInfo = [
  {
    icon: Phone,
    title: "Phone Numbers",
    details: ["+977-1-4567890", "+977-1-4567891"],
    description: "Call us during business hours"
  },
  {
    icon: Mail,
    title: "Email Addresses",
    details: ["<EMAIL>", "<EMAIL>"],
    description: "We respond within 24 hours"
  },
  {
    icon: MapPin,
    title: "Office Location",
    details: ["Kathmandu, Nepal", "Near Ratna Park"],
    description: "Visit us for project consultation"
  },
  {
    icon: Clock,
    title: "Business Hours",
    details: ["Mon-Fri: 9:00 AM - 6:00 PM", "Sat: 9:00 AM - 4:00 PM"],
    description: "Sunday closed"
  }
];

const services = [
  "Residential Construction",
  "Commercial Buildings", 
  "Infrastructure Projects",
  "Renovation & Maintenance",
  "Interior Design",
  "Construction Consultation"
];

export default function Contact() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    service: "",
    projectType: "",
    budget: "",
    timeline: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    setIsSubmitting(false);
    setIsSubmitted(true);
    
    // Reset form after 3 seconds
    setTimeout(() => {
      setIsSubmitted(false);
      setFormData({
        name: "",
        email: "",
        phone: "",
        service: "",
        projectType: "",
        budget: "",
        timeline: "",
        message: ""
      });
    }, 3000);
  };

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <MessageSquare className="w-4 h-4 mr-2" />
              Contact Us
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Let's Build Your{" "}
              <span className="text-orange-400">Dream Project</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              Ready to start your construction project? Get in touch with our expert team 
              for a free consultation and detailed project quote.
            </p>
          </div>
        </div>
      </section>

      {/* Contact Information */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {contactInfo.map((info, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="flex justify-center mb-4">
                    <div className="p-4 bg-blue-100 rounded-full">
                      <info.icon className="h-8 w-8 text-blue-600" />
                    </div>
                  </div>
                  <h3 className="text-lg font-semibold mb-3 text-slate-900">
                    {info.title}
                  </h3>
                  <div className="space-y-1 mb-3">
                    {info.details.map((detail, detailIndex) => (
                      <p key={detailIndex} className="text-slate-700 font-medium">
                        {detail}
                      </p>
                    ))}
                  </div>
                  <p className="text-sm text-slate-600">{info.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Contact Form and Map */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="grid lg:grid-cols-2 gap-12">
            {/* Contact Form */}
            <Card className="p-8">
              <CardHeader className="p-0 mb-8">
                <CardTitle className="text-2xl text-slate-900 flex items-center">
                  <Send className="h-6 w-6 mr-3 text-blue-600" />
                  Get Free Project Quote
                </CardTitle>
                <p className="text-slate-600 mt-2">
                  Fill out the form below and we'll get back to you within 24 hours with a detailed quote.
                </p>
              </CardHeader>
              
              {isSubmitted ? (
                <div className="text-center py-12">
                  <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold text-slate-900 mb-2">
                    Thank You!
                  </h3>
                  <p className="text-slate-600">
                    Your message has been sent successfully. We'll contact you soon.
                  </p>
                </div>
              ) : (
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="flex items-center mb-2">
                        <User className="h-4 w-4 mr-2" />
                        Full Name *
                      </Label>
                      <Input
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter your full name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="flex items-center mb-2">
                        <Mail className="h-4 w-4 mr-2" />
                        Email Address *
                      </Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter your email"
                      />
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="phone" className="flex items-center mb-2">
                        <Phone className="h-4 w-4 mr-2" />
                        Phone Number *
                      </Label>
                      <Input
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleInputChange}
                        required
                        placeholder="Enter your phone number"
                      />
                    </div>
                    <div>
                      <Label htmlFor="service" className="flex items-center mb-2">
                        <Building className="h-4 w-4 mr-2" />
                        Service Needed *
                      </Label>
                      <select
                        id="service"
                        name="service"
                        value={formData.service}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select a service</option>
                        {services.map((service, index) => (
                          <option key={index} value={service}>
                            {service}
                          </option>
                        ))}
                      </select>
                    </div>
                  </div>

                  <div className="grid md:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="budget" className="mb-2 block">
                        Project Budget (NPR)
                      </Label>
                      <select
                        id="budget"
                        name="budget"
                        value={formData.budget}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select budget range</option>
                        <option value="under-1m">Under 10 Lakh</option>
                        <option value="1m-5m">10 Lakh - 50 Lakh</option>
                        <option value="5m-1cr">50 Lakh - 1 Crore</option>
                        <option value="1cr-plus">1 Crore+</option>
                      </select>
                    </div>
                    <div>
                      <Label htmlFor="timeline" className="flex items-center mb-2">
                        <Calendar className="h-4 w-4 mr-2" />
                        Project Timeline
                      </Label>
                      <select
                        id="timeline"
                        name="timeline"
                        value={formData.timeline}
                        onChange={handleInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      >
                        <option value="">Select timeline</option>
                        <option value="asap">ASAP</option>
                        <option value="1-3months">1-3 months</option>
                        <option value="3-6months">3-6 months</option>
                        <option value="6months-plus">6+ months</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="message" className="mb-2 block">
                      Project Details *
                    </Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleInputChange}
                      required
                      rows={5}
                      placeholder="Please describe your project requirements, location, and any specific needs..."
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="w-full bg-blue-600 hover:bg-blue-700" 
                    size="lg"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      "Sending Message..."
                    ) : (
                      <>
                        <Send className="h-5 w-5 mr-2" />
                        Send Message & Get Quote
                      </>
                    )}
                  </Button>
                </form>
              )}
            </Card>

            {/* Map and Additional Info */}
            <div className="space-y-8">
              {/* Map Placeholder */}
              <Card className="p-8">
                <CardHeader className="p-0 mb-6">
                  <CardTitle className="text-xl text-slate-900 flex items-center">
                    <MapPin className="h-5 w-5 mr-2 text-blue-600" />
                    Our Location
                  </CardTitle>
                </CardHeader>
                <div className="bg-slate-200 rounded-lg h-64 flex items-center justify-center">
                  <div className="text-center">
                    <MapPin className="h-12 w-12 text-slate-400 mx-auto mb-2" />
                    <p className="text-slate-600">Interactive Map</p>
                    <p className="text-sm text-slate-500">Kathmandu, Nepal</p>
                  </div>
                </div>
                <div className="mt-6 space-y-3">
                  <div className="flex items-start space-x-3">
                    <MapPin className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-slate-900">Head Office</p>
                      <p className="text-slate-600">Ratna Park, Kathmandu, Nepal</p>
                    </div>
                  </div>
                  <div className="flex items-start space-x-3">
                    <Building className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <p className="font-medium text-slate-900">Project Office</p>
                      <p className="text-slate-600">Multiple locations across Nepal</p>
                    </div>
                  </div>
                </div>
              </Card>

              {/* Quick Contact */}
              <Card className="p-6 bg-blue-600 text-white">
                <CardHeader className="p-0 mb-4">
                  <CardTitle className="text-xl">Need Immediate Assistance?</CardTitle>
                </CardHeader>
                <CardContent className="p-0">
                  <p className="mb-4">
                    For urgent construction needs or emergency repairs, call us directly.
                  </p>
                  <div className="space-y-2">
                    <Button asChild variant="secondary" className="w-full">
                      <a href="tel:+977-1-4567890">
                        <Phone className="h-4 w-4 mr-2" />
                        Call Now: +977-1-4567890
                      </a>
                    </Button>
                    <Button asChild variant="outline" className="w-full border-white text-white hover:bg-white hover:text-blue-600">
                      <a href="mailto:<EMAIL>">
                        <Mail className="h-4 w-4 mr-2" />
                        Email Us
                      </a>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
