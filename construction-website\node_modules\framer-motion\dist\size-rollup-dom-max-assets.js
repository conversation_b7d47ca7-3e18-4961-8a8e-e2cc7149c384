import{createContext as t}from"react";const r=(t,r,a)=>a>r?r:a<t?t:a,a=t({});function e(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function o(t){return"string"==typeof t||Array.isArray(t)}const s=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],n=["initial",...s];function i(t){return e(t.animate)||n.some(r=>o(t[r]))}function f(t){return Boolean(i(t)||t.variants)}const l=t=>r=>"string"==typeof r&&r.startsWith(t),c=l("--"),p=l("var(--"),d=t=>!!p(t)&&u.test(t.split("/*")[0].trim()),u=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,m={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},g={...m,transform:t=>r(0,1,t)},h={...m,default:1},y=t=>({test:r=>"string"==typeof r&&r.endsWith(t)&&1===r.split(" ").length,parse:parseFloat,transform:r=>`${r}${t}`}),w=y("deg"),v=y("%"),b=y("px"),x=y("vh"),k=y("vw"),B=(()=>({...v,parse:t=>v.parse(t)/100,transform:t=>v.transform(100*t)}))(),O=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],X=(()=>new Set(O))(),Y={...m,transform:Math.round},S={borderWidth:b,borderTopWidth:b,borderRightWidth:b,borderBottomWidth:b,borderLeftWidth:b,borderRadius:b,radius:b,borderTopLeftRadius:b,borderTopRightRadius:b,borderBottomRightRadius:b,borderBottomLeftRadius:b,width:b,maxWidth:b,height:b,maxHeight:b,top:b,right:b,bottom:b,left:b,padding:b,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b,margin:b,marginTop:b,marginRight:b,marginBottom:b,marginLeft:b,backgroundPositionX:b,backgroundPositionY:b,...{rotate:w,rotateX:w,rotateY:w,rotateZ:w,scale:h,scaleX:h,scaleY:h,scaleZ:h,skew:w,skewX:w,skewY:w,distance:b,translateX:b,translateY:b,translateZ:b,x:b,y:b,z:b,perspective:b,transformPerspective:b,opacity:g,originX:B,originY:B,originZ:b},zIndex:Y,fillOpacity:g,strokeOpacity:g,numOctaves:Y},$=(t,r)=>r&&"number"==typeof t?r.transform(t):t,L=t=>Boolean(t&&t.getVelocity),P={};function T(t){for(const r in t)P[r]=t[r],c(r)&&(P[r].isCSSVariable=!0)}function W(t,{layout:r,layoutId:a}){return X.has(t)||t.startsWith("origin")||(r||void 0!==a)&&(!!P[t]||"opacity"===t)}const R={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},V=O.length;function Z(t,r,a){const{style:e,vars:o,transformOrigin:s}=t;let n=!1,i=!1;for(const t in r){const a=r[t];if(X.has(t))n=!0;else if(c(t))o[t]=a;else{const r=$(a,S[t]);t.startsWith("origin")?(i=!0,s[t]=r):e[t]=r}}if(r.transform||(n||a?e.transform=function(t,r,a){let e="",o=!0;for(let s=0;s<V;s++){const n=O[s],i=t[n];if(void 0===i)continue;let f=!0;if(f="number"==typeof i?i===(n.startsWith("scale")?1:0):0===parseFloat(i),!f||a){const t=$(i,S[n]);f||(o=!1,e+=`${R[n]||n}(${t}) `),a&&(r[n]=t)}}return e=e.trim(),a?e=a(r,o?"":e):o&&(e="none"),e}(r,t.transform,a):e.transform&&(e.transform="none")),i){const{originX:t="50%",originY:r="50%",originZ:a=0}=s;e.transformOrigin=`${t} ${r} ${a}`}}const I={offset:"stroke-dashoffset",array:"stroke-dasharray"},A={offset:"strokeDashoffset",array:"strokeDasharray"};function C(t,{attrX:r,attrY:a,attrScale:e,pathLength:o,pathSpacing:s=1,pathOffset:n=0,...i},f,l,c){if(Z(t,i,l),f)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:p,style:d}=t;p.transform&&(d.transform=p.transform,delete p.transform),(d.transform||p.transformOrigin)&&(d.transformOrigin=p.transformOrigin??"50% 50%",delete p.transformOrigin),d.transform&&(d.transformBox=c?.transformBox??"fill-box",delete p.transformBox),void 0!==r&&(p.x=r),void 0!==a&&(p.y=a),void 0!==e&&(p.scale=e),void 0!==o&&function(t,r,a=1,e=0,o=!0){t.pathLength=1;const s=o?I:A;t[s.offset]=b.transform(-e);const n=b.transform(r),i=b.transform(a);t[s.array]=`${n} ${i}`}(p,o,s,n,!1)}const F=t=>"string"==typeof t&&"svg"===t.toLowerCase(),H=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function z(t){return"string"==typeof t&&!t.includes("-")&&!!(H.indexOf(t)>-1||/[A-Z]/u.test(t))}const E=t(null);function D(t){const r=[{},{}];return t?.values.forEach((t,a)=>{r[0][a]=t.get(),r[1][a]=t.getVelocity()}),r}function j(t,r,a,e){if("function"==typeof r){const[o,s]=D(e);r=r(void 0!==a?a:t.custom,o,s)}if("string"==typeof r&&(r=t.variants&&t.variants[r]),"function"==typeof r){const[o,s]=D(e);r=r(void 0!==a?a:t.custom,o,s)}return r}function q(t){return L(t)?t.get():t}function G(t,r,a){const{style:e}=t,o={};for(const s in e)(L(e[s])||r.style&&L(r.style[s])||W(s,t)||void 0!==a?.getValue(s)?.liveStyle)&&(o[s]=e[s]);return o}function J(t,r,a){const e=G(t,r,a);for(const a in t)if(L(t[a])||L(r[a])){e[-1!==O.indexOf(a)?"attr"+a.charAt(0).toUpperCase()+a.substring(1):a]=t[a]}return e}const M="undefined"!=typeof window,U={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},K={};for(const t in U)K[t]={isEnabled:r=>U[t].some(t=>!!r[t])};function N(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}const Q=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),_="data-"+Q("framerAppearId"),tt=t({});export{k as A,x as B,S as C,X as D,P as E,c as F,T as G,n as H,s as I,Q as J,a as L,E as P,tt as S,o as a,L as b,W as c,Z as d,C as e,F as f,z as g,f as h,i,e as j,j as k,J as l,K as m,N as n,M as o,_ as p,m as q,q as r,G as s,g as t,r as u,v,d as w,b as x,O as y,w as z};
