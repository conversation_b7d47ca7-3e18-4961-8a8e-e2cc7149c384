(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[893],{191:(e,t,a)=>{Promise.resolve().then(a.bind(a,2243))},285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(5155);a(2115);var r=a(9708),i=a(2085),l=a(9434);let n=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:i,asChild:o=!1,...d}=e,c=o?r.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,l.cn)(n({variant:a,size:i,className:t})),...d})}},646:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2243:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>y});var s=a(5155),r=a(2115),i=a(285),l=a(6695),n=a(6126),o=a(7340),d=a(8136),c=a(7957),u=a(6932),m=a(646),h=a(4186),x=a(4516),p=a(9074),v=a(7580);let g=[{id:1,title:"Sunrise Residential Complex",location:"Kathmandu, Nepal",type:"Residential",status:"Completed",year:"2024",description:"50-unit modern residential complex with amenities including parking, garden, and community hall.",features:["50 residential units","Underground parking","Community garden","24/7 security"],duration:"18 months",client:"Sunrise Development Pvt. Ltd."},{id:2,title:"Tech Hub Office Building",location:"Pokhara, Nepal",type:"Commercial",status:"Completed",year:"2023",description:"5-story modern office building with retail spaces on ground floor and premium office suites.",features:["5 floors","Retail spaces","Premium offices","Modern elevators"],duration:"12 months",client:"Tech Hub Nepal"},{id:3,title:"Mountain Highway Bridge",location:"Chitwan, Nepal",type:"Infrastructure",status:"Ongoing",year:"2024",description:"Major bridge construction project connecting rural mountain communities to main highway.",features:["200m span bridge","Heavy load capacity","Seismic resistant","Rural connectivity"],duration:"24 months",client:"Department of Roads"},{id:4,title:"Heritage Hotel Renovation",location:"Bhaktapur, Nepal",type:"Renovation",status:"Completed",year:"2023",description:"Complete renovation of historic hotel while preserving traditional Newari architecture.",features:["Heritage preservation","Modern amenities","Traditional design","Structural upgrade"],duration:"8 months",client:"Heritage Hotels Nepal"},{id:5,title:"Green Valley Apartments",location:"Lalitpur, Nepal",type:"Residential",status:"Ongoing",year:"2024",description:"Eco-friendly apartment complex with solar panels and rainwater harvesting systems.",features:["30 apartments","Solar power","Rainwater harvesting","Green building"],duration:"15 months",client:"Green Valley Developers"},{id:6,title:"City Mall Extension",location:"Kathmandu, Nepal",type:"Commercial",status:"Completed",year:"2022",description:"Extension and renovation of existing shopping mall with new retail spaces and food court.",features:["New retail spaces","Food court","Parking expansion","Modern facade"],duration:"10 months",client:"City Mall Pvt. Ltd."},{id:7,title:"Rural Road Network",location:"Gorkha, Nepal",type:"Infrastructure",status:"Completed",year:"2023",description:"Construction of 15km rural road network connecting remote villages to district headquarters.",features:["15km road network","Bridge construction","Drainage systems","Village connectivity"],duration:"20 months",client:"Local Government"},{id:8,title:"Modern Family Home",location:"Biratnagar, Nepal",type:"Residential",status:"Completed",year:"2024",description:"Custom-designed modern family home with contemporary architecture and smart home features.",features:["Smart home system","Modern design","Energy efficient","Custom interiors"],duration:"6 months",client:"Private Client"}],f=[{label:"All Projects",value:"all"},{label:"Residential",value:"Residential"},{label:"Commercial",value:"Commercial"},{label:"Infrastructure",value:"Infrastructure"},{label:"Renovation",value:"Renovation"}],b=[{label:"All Status",value:"all"},{label:"Completed",value:"Completed"},{label:"Ongoing",value:"Ongoing"}];function y(){let[e,t]=(0,r.useState)("all"),[a,y]=(0,r.useState)("all"),j=g.filter(t=>{let s="all"===e||t.type===e,r="all"===a||t.status===a;return s&&r});return(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("section",{className:"relative bg-slate-900 text-white py-20",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsxs)(n.E,{className:"mb-6 bg-orange-500 hover:bg-orange-600",children:[(0,s.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Our Projects"]}),(0,s.jsxs)("h1",{className:"text-4xl md:text-5xl font-bold mb-6",children:["Building Excellence Across"," ",(0,s.jsx)("span",{className:"text-orange-400",children:"Nepal"})]}),(0,s.jsx)("p",{className:"text-xl text-slate-200 leading-relaxed",children:"Explore our portfolio of successful construction projects spanning residential, commercial, and infrastructure developments throughout Nepal."})]})})}),(0,s.jsx)("section",{className:"py-16 bg-white",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 text-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-2",children:"150+"}),(0,s.jsx)("div",{className:"text-slate-600",children:"Total Projects"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-2",children:"98%"}),(0,s.jsx)("div",{className:"text-slate-600",children:"On-Time Delivery"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-2",children:"25+"}),(0,s.jsx)("div",{className:"text-slate-600",children:"Cities Covered"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-4xl font-bold text-blue-600 mb-2",children:"100%"}),(0,s.jsx)("div",{className:"text-slate-600",children:"Client Satisfaction"})]})]})})}),(0,s.jsx)("section",{className:"py-8 bg-slate-50 border-b",children:(0,s.jsx)("div",{className:"container mx-auto px-4",children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4 items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(u.A,{className:"h-5 w-5 text-slate-600"}),(0,s.jsx)("span",{className:"font-medium text-slate-700",children:"Filter Projects:"})]}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:f.map(a=>(0,s.jsx)(i.$,{variant:e===a.value?"default":"outline",size:"sm",onClick:()=>t(a.value),className:e===a.value?"bg-blue-600 hover:bg-blue-700":"",children:a.label},a.value))}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2",children:b.map(e=>(0,s.jsx)(i.$,{variant:a===e.value?"default":"outline",size:"sm",onClick:()=>y(e.value),className:a===e.value?"bg-orange-600 hover:bg-orange-700":"",children:e.label},e.value))})]})})}),(0,s.jsx)("section",{className:"py-20 bg-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-4",children:[(0,s.jsx)("div",{className:"mb-8",children:(0,s.jsxs)("p",{className:"text-slate-600",children:["Showing ",j.length," of ",g.length," projects"]})}),(0,s.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:j.map(e=>{let t=(e=>{switch(e){case"Residential":return o.A;case"Commercial":default:return d.A;case"Infrastructure":return c.A}})(e.type);return(0,s.jsxs)(l.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[(0,s.jsxs)("div",{className:"relative h-48 bg-gradient-to-br from-slate-200 to-slate-300 flex items-center justify-center",children:[(0,s.jsx)(t,{className:"h-16 w-16 text-slate-400"}),(0,s.jsx)("div",{className:"absolute top-4 right-4",children:(0,s.jsxs)(n.E,{variant:"Completed"===e.status?"default":"secondary",children:["Completed"===e.status?(0,s.jsx)(m.A,{className:"w-3 h-3 mr-1"}):(0,s.jsx)(h.A,{className:"w-3 h-3 mr-1"}),e.status]})}),(0,s.jsx)("div",{className:"absolute top-4 left-4",children:(0,s.jsx)(n.E,{variant:"outline",className:"bg-white/90",children:e.type})})]}),(0,s.jsxs)(l.Wu,{className:"p-6",children:[(0,s.jsx)("h3",{className:"text-xl font-semibold mb-2 text-slate-900",children:e.title}),(0,s.jsxs)("div",{className:"flex items-center text-slate-600 mb-2",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 mr-1"}),(0,s.jsx)("span",{className:"text-sm",children:e.location})]}),(0,s.jsxs)("div",{className:"flex items-center text-slate-600 mb-4",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-1"}),(0,s.jsxs)("span",{className:"text-sm",children:[e.year," • ",e.duration]})]}),(0,s.jsx)("p",{className:"text-slate-700 text-sm mb-4 leading-relaxed",children:e.description}),(0,s.jsx)("div",{className:"space-y-2 mb-4",children:e.features.slice(0,3).map((e,t)=>(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(m.A,{className:"h-3 w-3 text-green-500 flex-shrink-0"}),(0,s.jsx)("span",{className:"text-xs text-slate-600",children:e})]},t))}),(0,s.jsx)("div",{className:"flex items-center justify-between pt-4 border-t",children:(0,s.jsxs)("div",{className:"flex items-center text-slate-600",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-1"}),(0,s.jsxs)("span",{className:"text-xs",children:["Client: ",e.client]})]})})]})]},e.id)})})]})}),(0,s.jsx)("section",{className:"py-20 bg-blue-600 text-white",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 text-center",children:[(0,s.jsx)("h2",{className:"text-3xl md:text-4xl font-bold mb-6",children:"Ready to Start Your Next Project?"}),(0,s.jsx)("p",{className:"text-xl mb-8 max-w-2xl mx-auto",children:"Join our satisfied clients and let us bring your construction vision to life."}),(0,s.jsx)(i.$,{asChild:!0,size:"lg",className:"bg-orange-500 hover:bg-orange-600",children:(0,s.jsx)("a",{href:"/contact",children:"Start Your Project Today"})})]})})]})}},4186:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},4516:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},6126:(e,t,a)=>{"use strict";a.d(t,{E:()=>o});var s=a(5155);a(2115);var r=a(9708),i=a(2085),l=a(9434);let n=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:t,variant:a,asChild:i=!1,...o}=e,d=i?r.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,l.cn)(n({variant:a}),t),...o})}},6695:(e,t,a)=>{"use strict";a.d(t,{Wu:()=>o,ZB:()=>n,Zp:()=>i,aR:()=>l});var s=a(5155);a(2115);var r=a(9434);function i(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card",className:(0,r.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function l(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,r.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function n(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,r.cn)("leading-none font-semibold",t),...a})}function o(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,r.cn)("px-6",t),...a})}},6932:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]])},7340:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7580:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},7957:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("construction",[["rect",{x:"2",y:"6",width:"20",height:"8",rx:"1",key:"1estib"}],["path",{d:"M17 14v7",key:"7m2elx"}],["path",{d:"M7 14v7",key:"1cm7wv"}],["path",{d:"M17 3v3",key:"1v4jwn"}],["path",{d:"M7 3v3",key:"7o6guu"}],["path",{d:"M10 14 2.3 6.3",key:"1023jk"}],["path",{d:"m14 6 7.7 7.7",key:"1s8pl2"}],["path",{d:"m8 6 8 8",key:"hl96qh"}]])},8136:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]])},9074:(e,t,a)=>{"use strict";a.d(t,{A:()=>s});let s=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,t,a)=>{"use strict";a.d(t,{cn:()=>i});var s=a(2596),r=a(9688);function i(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,r.QP)((0,s.$)(t))}}},e=>{e.O(0,[455,441,964,358],()=>e(e.s=191)),_N_E=e.O()}]);