"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Home,
  Building,
  Construction,
  Hammer,
  Users,
  Camera,
  Filter,
  Eye
} from "lucide-react";

const galleryItems = [
  {
    id: 1,
    title: "Modern Residential Complex",
    category: "Residential",
    type: "Exterior",
    description: "Contemporary apartment building with modern facade design",
    location: "Kathmandu"
  },
  {
    id: 2,
    title: "Construction Site Progress",
    category: "Construction",
    type: "Progress",
    description: "Active construction site showing structural work in progress",
    location: "Pokhara"
  },
  {
    id: 3,
    title: "Office Building Interior",
    category: "Commercial",
    type: "Interior",
    description: "Modern office space with contemporary design elements",
    location: "Lalitpur"
  },
  {
    id: 4,
    title: "Bridge Construction",
    category: "Infrastructure",
    type: "Construction",
    description: "Major bridge construction project over river",
    location: "Chitwan"
  },
  {
    id: 5,
    title: "Luxury Home Exterior",
    category: "Residential",
    type: "Exterior",
    description: "Custom designed luxury home with landscaping",
    location: "Bhaktapur"
  },
  {
    id: 6,
    title: "Team at Work",
    category: "Team",
    type: "People",
    description: "Our skilled construction team working on site",
    location: "Various"
  },
  {
    id: 7,
    title: "Shopping Mall Interior",
    category: "Commercial",
    type: "Interior",
    description: "Modern shopping mall with contemporary interior design",
    location: "Kathmandu"
  },
  {
    id: 8,
    title: "Road Construction",
    category: "Infrastructure",
    type: "Construction",
    description: "Highway construction project connecting rural areas",
    location: "Gorkha"
  },
  {
    id: 9,
    title: "Residential Kitchen",
    category: "Residential",
    type: "Interior",
    description: "Modern kitchen design with premium finishes",
    location: "Pokhara"
  },
  {
    id: 10,
    title: "Construction Materials",
    category: "Materials",
    type: "Resources",
    description: "Quality construction materials and equipment",
    location: "Warehouse"
  },
  {
    id: 11,
    title: "Heritage Renovation",
    category: "Renovation",
    type: "Restoration",
    description: "Traditional building renovation preserving heritage",
    location: "Bhaktapur"
  },
  {
    id: 12,
    title: "Project Completion",
    category: "Completed",
    type: "Final",
    description: "Completed residential project with landscaping",
    location: "Lalitpur"
  }
];

const categories = [
  { label: "All", value: "all", icon: Camera },
  { label: "Residential", value: "Residential", icon: Home },
  { label: "Commercial", value: "Commercial", icon: Building },
  { label: "Infrastructure", value: "Infrastructure", icon: Construction },
  { label: "Construction", value: "Construction", icon: Hammer },
  { label: "Team", value: "Team", icon: Users }
];

export default function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState("all");

  const filteredItems = galleryItems.filter(item => 
    selectedCategory === "all" || item.category === selectedCategory
  );

  const getCategoryIcon = (category: string) => {
    const categoryData = categories.find(cat => cat.value === category);
    return categoryData?.icon || Camera;
  };

  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative bg-slate-900 text-white py-20">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge className="mb-6 bg-orange-500 hover:bg-orange-600">
              <Camera className="w-4 h-4 mr-2" />
              Project Gallery
            </Badge>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Visual Journey of Our{" "}
              <span className="text-orange-400">Construction Excellence</span>
            </h1>
            <p className="text-xl text-slate-200 leading-relaxed">
              Explore our construction projects through images showcasing quality workmanship, 
              innovative designs, and successful project completions across Nepal.
            </p>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-8 bg-slate-50 border-b">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-2">
              <Filter className="h-5 w-5 text-slate-600" />
              <span className="font-medium text-slate-700">Filter by Category:</span>
            </div>
            <div className="flex flex-wrap gap-2">
              {categories.map((category) => {
                const IconComponent = category.icon;
                return (
                  <Button
                    key={category.value}
                    variant={selectedCategory === category.value ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedCategory(category.value)}
                    className={selectedCategory === category.value ? "bg-blue-600 hover:bg-blue-700" : ""}
                  >
                    <IconComponent className="w-4 h-4 mr-2" />
                    {category.label}
                  </Button>
                );
              })}
            </div>
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="mb-8">
            <p className="text-slate-600">
              Showing {filteredItems.length} of {galleryItems.length} images
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map((item) => {
              const CategoryIcon = getCategoryIcon(item.category);
              return (
                <Card key={item.id} className="overflow-hidden hover:shadow-lg transition-all duration-300 group">
                  <div className="relative h-64 bg-gradient-to-br from-slate-200 to-slate-400 flex items-center justify-center">
                    <CategoryIcon className="h-16 w-16 text-slate-500 group-hover:scale-110 transition-transform duration-300" />
                    
                    {/* Overlay on hover */}
                    <div className="absolute inset-0 bg-black/60 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">
                      <Button size="sm" className="bg-white text-slate-900 hover:bg-slate-100">
                        <Eye className="w-4 h-4 mr-2" />
                        View
                      </Button>
                    </div>
                    
                    {/* Category badge */}
                    <div className="absolute top-3 left-3">
                      <Badge variant="secondary" className="bg-white/90 text-slate-700">
                        {item.category}
                      </Badge>
                    </div>
                    
                    {/* Type badge */}
                    <div className="absolute top-3 right-3">
                      <Badge variant="outline" className="bg-white/90 border-slate-300">
                        {item.type}
                      </Badge>
                    </div>
                  </div>
                  
                  <CardContent className="p-4">
                    <h3 className="font-semibold text-slate-900 mb-2 line-clamp-1">
                      {item.title}
                    </h3>
                    <p className="text-sm text-slate-600 mb-3 line-clamp-2">
                      {item.description}
                    </p>
                    <div className="flex items-center justify-between text-xs text-slate-500">
                      <span>{item.location}</span>
                      <span className="flex items-center">
                        <Camera className="w-3 h-3 mr-1" />
                        Photo
                      </span>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-slate-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold text-slate-900 mb-4">
              What Our Clients Say
            </h2>
            <p className="text-xl text-slate-600 max-w-2xl mx-auto">
              Hear from satisfied clients about their experience working with ConstructCo
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[
              {
                name: "Rajesh Sharma",
                role: "Homeowner",
                project: "Modern Family Home",
                content: "ConstructCo exceeded our expectations. The quality of work and attention to detail was outstanding. Our dream home became a reality.",
                rating: 5
              },
              {
                name: "Maya Patel",
                role: "Business Owner",
                project: "Office Building",
                content: "Professional team, on-time delivery, and excellent communication throughout the project. Highly recommend their services.",
                rating: 5
              },
              {
                name: "Prakash Thapa",
                role: "Developer",
                project: "Residential Complex",
                content: "Working with ConstructCo was a pleasure. They handled our 50-unit complex project with expertise and professionalism.",
                rating: 5
              }
            ].map((testimonial, index) => (
              <Card key={index} className="p-6 hover:shadow-lg transition-shadow">
                <CardContent className="p-0">
                  <div className="flex items-center mb-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-slate-600 rounded-full flex items-center justify-center text-white font-bold">
                      {testimonial.name.charAt(0)}
                    </div>
                    <div className="ml-4">
                      <h4 className="font-semibold text-slate-900">{testimonial.name}</h4>
                      <p className="text-sm text-slate-600">{testimonial.role}</p>
                    </div>
                  </div>
                  <p className="text-slate-700 mb-4 leading-relaxed">
                    "                    &ldquo;{testimonial.content}&rdquo;"
                  </p>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-500">Project: {testimonial.project}</span>
                    <div className="flex">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <span key={i} className="text-yellow-400">★</span>
                      ))}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-6">
            Ready to Create Your Success Story?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join our gallery of successful projects and satisfied clients.
          </p>
          <Button asChild size="lg" className="bg-orange-500 hover:bg-orange-600">
            <a href="/contact">
              Start Your Project
            </a>
          </Button>
        </div>
      </section>
    </div>
  );
}
