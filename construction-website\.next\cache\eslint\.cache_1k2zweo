[{"C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\careers\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\contact\\layout.tsx": "3", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\contact\\page.tsx": "4", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\faq\\layout.tsx": "5", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\faq\\page.tsx": "6", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\gallery\\layout.tsx": "7", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\gallery\\page.tsx": "8", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\layout.tsx": "9", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\page.tsx": "10", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\projects\\layout.tsx": "11", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\projects\\page.tsx": "12", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\robots.ts": "13", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\services\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\sitemap.ts": "15", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\footer.tsx": "16", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\header.tsx": "17", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\badge.tsx": "18", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\button.tsx": "19", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\card.tsx": "20", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\form.tsx": "21", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\input.tsx": "22", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\label.tsx": "23", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\navigation-menu.tsx": "24", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\separator.tsx": "25", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\sheet.tsx": "26", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\textarea.tsx": "27", "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\lib\\utils.ts": "28"}, {"size": 12483, "mtime": 1753333669021, "results": "29", "hashOfConfig": "30"}, {"size": 13367, "mtime": 1753333727492, "results": "31", "hashOfConfig": "30"}, {"size": 584, "mtime": 1753333718643, "results": "32", "hashOfConfig": "30"}, {"size": 15249, "mtime": 1753333470763, "results": "33", "hashOfConfig": "30"}, {"size": 581, "mtime": 1753333737850, "results": "34", "hashOfConfig": "30"}, {"size": 13126, "mtime": 1753333591550, "results": "35", "hashOfConfig": "30"}, {"size": 585, "mtime": 1753333712689, "results": "36", "hashOfConfig": "30"}, {"size": 12318, "mtime": 1753338984402, "results": "37", "hashOfConfig": "30"}, {"size": 1037, "mtime": 1753332891722, "results": "38", "hashOfConfig": "30"}, {"size": 15083, "mtime": 1753340042761, "results": "39", "hashOfConfig": "30"}, {"size": 624, "mtime": 1753333705109, "results": "40", "hashOfConfig": "30"}, {"size": 12430, "mtime": 1753339059891, "results": "41", "hashOfConfig": "30"}, {"size": 362, "mtime": 1753340251012, "results": "42", "hashOfConfig": "30"}, {"size": 13620, "mtime": 1753339030931, "results": "43", "hashOfConfig": "30"}, {"size": 1278, "mtime": 1753340262165, "results": "44", "hashOfConfig": "30"}, {"size": 5330, "mtime": 1753335325683, "results": "45", "hashOfConfig": "30"}, {"size": 3948, "mtime": 1753332847014, "results": "46", "hashOfConfig": "30"}, {"size": 1631, "mtime": 1753332763555, "results": "47", "hashOfConfig": "30"}, {"size": 2123, "mtime": 1753332763381, "results": "48", "hashOfConfig": "30"}, {"size": 1989, "mtime": 1753332763402, "results": "49", "hashOfConfig": "30"}, {"size": 3759, "mtime": 1753332763458, "results": "50", "hashOfConfig": "30"}, {"size": 967, "mtime": 1753332763407, "results": "51", "hashOfConfig": "30"}, {"size": 611, "mtime": 1753332763423, "results": "52", "hashOfConfig": "30"}, {"size": 6664, "mtime": 1753332763534, "results": "53", "hashOfConfig": "30"}, {"size": 699, "mtime": 1753332763562, "results": "54", "hashOfConfig": "30"}, {"size": 4090, "mtime": 1753332763546, "results": "55", "hashOfConfig": "30"}, {"size": 759, "mtime": 1753332763414, "results": "56", "hashOfConfig": "30"}, {"size": 166, "mtime": 1753332722330, "results": "57", "hashOfConfig": "30"}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "2orf3k", {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\about\\page.tsx", ["142", "143", "144"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\careers\\page.tsx", ["145"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\contact\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\contact\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\faq\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\faq\\page.tsx", ["146"], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\gallery\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\gallery\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\projects\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\robots.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\services\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\app\\sitemap.ts", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\form.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\label.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\navigation-menu.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\separator.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\sheet.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\components\\ui\\textarea.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\ashish\\project\\construction\\construction-website\\src\\lib\\utils.ts", [], [], {"ruleId": "147", "severity": 1, "message": "148", "line": 4, "column": 10, "nodeType": null, "messageId": "149", "endLine": 4, "endColumn": 19}, {"ruleId": "147", "severity": 1, "message": "150", "line": 21, "column": 3, "nodeType": null, "messageId": "149", "endLine": 21, "endColumn": 9}, {"ruleId": "147", "severity": 1, "message": "151", "line": 22, "column": 3, "nodeType": null, "messageId": "149", "endLine": 22, "endColumn": 14}, {"ruleId": "147", "severity": 1, "message": "152", "line": 16, "column": 3, "nodeType": null, "messageId": "149", "endLine": 16, "endColumn": 8}, {"ruleId": "147", "severity": 1, "message": "153", "line": 11, "column": 3, "nodeType": null, "messageId": "149", "endLine": 11, "endColumn": 11}, "@typescript-eslint/no-unused-vars", "'Separator' is defined but never used.", "unusedVar", "'MapPin' is defined but never used.", "'CheckCircle' is defined but never used.", "'Users' is defined but never used.", "'Building' is defined but never used."]