{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/app/contact/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Contact Us - ConstructCo | Get Free Construction Quote Nepal\",\n  description: \"Contact ConstructCo for your construction needs in Nepal. Get free project consultation and quotes for residential, commercial, and infrastructure construction projects.\",\n  keywords: \"contact construction company nepal, free construction quote, building contractor contact, construction consultation nepal\",\n};\n\nexport default function ContactLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,cAAc,EACpC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}