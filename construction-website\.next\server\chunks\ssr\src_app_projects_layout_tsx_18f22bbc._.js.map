{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/app/projects/layout.tsx"], "sourcesContent": ["import { Metadata } from \"next\";\n\nexport const metadata: Metadata = {\n  title: \"Our Projects - ConstructCo | Construction Portfolio Nepal\",\n  description: \"Explore ConstructCo's portfolio of successful construction projects across Nepal. Residential complexes, commercial buildings, infrastructure projects, and renovations completed with excellence.\",\n  keywords: \"construction projects nepal, building portfolio, completed projects kathmandu, construction gallery, residential commercial infrastructure\",\n};\n\nexport default function ProjectsLayout({\n  children,\n}: {\n  children: React.ReactNode;\n}) {\n  return children;\n}\n"], "names": [], "mappings": ";;;;AAEO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;IACb,UAAU;AACZ;AAEe,SAAS,eAAe,EACrC,QAAQ,EAGT;IACC,OAAO;AACT", "debugId": null}}]}