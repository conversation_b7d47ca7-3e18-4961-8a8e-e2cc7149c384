{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 147, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,8OAAC,iKAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 173, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 217, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/project/construction/construction-website/src/app/contact/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Input } from \"@/components/ui/input\";\nimport { Textarea } from \"@/components/ui/textarea\";\nimport { Label } from \"@/components/ui/label\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  Phone,\n  Mail,\n  MapPin,\n  Clock,\n  Send,\n  MessageSquare,\n  Building,\n  User,\n  Calendar,\n  CheckCircle\n} from \"lucide-react\";\n\nconst contactInfo = [\n  {\n    icon: Phone,\n    title: \"Phone Numbers\",\n    details: [\"+977-1-4567890\", \"+977-1-4567891\"],\n    description: \"Call us during business hours\"\n  },\n  {\n    icon: Mail,\n    title: \"Email Addresses\",\n    details: [\"<EMAIL>\", \"<EMAIL>\"],\n    description: \"We respond within 24 hours\"\n  },\n  {\n    icon: MapPin,\n    title: \"Office Location\",\n    details: [\"Kathmandu, Nepal\", \"Near Ratna Park\"],\n    description: \"Visit us for project consultation\"\n  },\n  {\n    icon: Clock,\n    title: \"Business Hours\",\n    details: [\"Mon-Fri: 9:00 AM - 6:00 PM\", \"Sat: 9:00 AM - 4:00 PM\"],\n    description: \"Sunday closed\"\n  }\n];\n\nconst services = [\n  \"Residential Construction\",\n  \"Commercial Buildings\", \n  \"Infrastructure Projects\",\n  \"Renovation & Maintenance\",\n  \"Interior Design\",\n  \"Construction Consultation\"\n];\n\nexport default function Contact() {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    email: \"\",\n    phone: \"\",\n    service: \"\",\n    projectType: \"\",\n    budget: \"\",\n    timeline: \"\",\n    message: \"\"\n  });\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [isSubmitted, setIsSubmitted] = useState(false);\n\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value\n    }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsSubmitting(true);\n    \n    // Simulate form submission\n    await new Promise(resolve => setTimeout(resolve, 2000));\n    \n    setIsSubmitting(false);\n    setIsSubmitted(true);\n    \n    // Reset form after 3 seconds\n    setTimeout(() => {\n      setIsSubmitted(false);\n      setFormData({\n        name: \"\",\n        email: \"\",\n        phone: \"\",\n        service: \"\",\n        projectType: \"\",\n        budget: \"\",\n        timeline: \"\",\n        message: \"\"\n      });\n    }, 3000);\n  };\n\n  return (\n    <div className=\"flex flex-col\">\n      {/* Hero Section */}\n      <section className=\"relative bg-slate-900 text-white py-20\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"max-w-4xl mx-auto text-center\">\n            <Badge className=\"mb-6 bg-orange-500 hover:bg-orange-600\">\n              <MessageSquare className=\"w-4 h-4 mr-2\" />\n              Contact Us\n            </Badge>\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-6\">\n              Let's Build Your{\" \"}\n              <span className=\"text-orange-400\">Dream Project</span>\n            </h1>\n            <p className=\"text-xl text-slate-200 leading-relaxed\">\n              Ready to start your construction project? Get in touch with our expert team \n              for a free consultation and detailed project quote.\n            </p>\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Information */}\n      <section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-8\">\n            {contactInfo.map((info, index) => (\n              <Card key={index} className=\"text-center p-6 hover:shadow-lg transition-shadow\">\n                <CardContent className=\"p-0\">\n                  <div className=\"flex justify-center mb-4\">\n                    <div className=\"p-4 bg-blue-100 rounded-full\">\n                      <info.icon className=\"h-8 w-8 text-blue-600\" />\n                    </div>\n                  </div>\n                  <h3 className=\"text-lg font-semibold mb-3 text-slate-900\">\n                    {info.title}\n                  </h3>\n                  <div className=\"space-y-1 mb-3\">\n                    {info.details.map((detail, detailIndex) => (\n                      <p key={detailIndex} className=\"text-slate-700 font-medium\">\n                        {detail}\n                      </p>\n                    ))}\n                  </div>\n                  <p className=\"text-sm text-slate-600\">{info.description}</p>\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n        </div>\n      </section>\n\n      {/* Contact Form and Map */}\n      <section className=\"py-20 bg-slate-50\">\n        <div className=\"container mx-auto px-4\">\n          <div className=\"grid lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <Card className=\"p-8\">\n              <CardHeader className=\"p-0 mb-8\">\n                <CardTitle className=\"text-2xl text-slate-900 flex items-center\">\n                  <Send className=\"h-6 w-6 mr-3 text-blue-600\" />\n                  Get Free Project Quote\n                </CardTitle>\n                <p className=\"text-slate-600 mt-2\">\n                  Fill out the form below and we'll get back to you within 24 hours with a detailed quote.\n                </p>\n              </CardHeader>\n              \n              {isSubmitted ? (\n                <div className=\"text-center py-12\">\n                  <CheckCircle className=\"h-16 w-16 text-green-500 mx-auto mb-4\" />\n                  <h3 className=\"text-xl font-semibold text-slate-900 mb-2\">\n                    Thank You!\n                  </h3>\n                  <p className=\"text-slate-600\">\n                    Your message has been sent successfully. We'll contact you soon.\n                  </p>\n                </div>\n              ) : (\n                <form onSubmit={handleSubmit} className=\"space-y-6\">\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"name\" className=\"flex items-center mb-2\">\n                        <User className=\"h-4 w-4 mr-2\" />\n                        Full Name *\n                      </Label>\n                      <Input\n                        id=\"name\"\n                        name=\"name\"\n                        value={formData.name}\n                        onChange={handleInputChange}\n                        required\n                        placeholder=\"Enter your full name\"\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"email\" className=\"flex items-center mb-2\">\n                        <Mail className=\"h-4 w-4 mr-2\" />\n                        Email Address *\n                      </Label>\n                      <Input\n                        id=\"email\"\n                        name=\"email\"\n                        type=\"email\"\n                        value={formData.email}\n                        onChange={handleInputChange}\n                        required\n                        placeholder=\"Enter your email\"\n                      />\n                    </div>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"phone\" className=\"flex items-center mb-2\">\n                        <Phone className=\"h-4 w-4 mr-2\" />\n                        Phone Number *\n                      </Label>\n                      <Input\n                        id=\"phone\"\n                        name=\"phone\"\n                        value={formData.phone}\n                        onChange={handleInputChange}\n                        required\n                        placeholder=\"Enter your phone number\"\n                      />\n                    </div>\n                    <div>\n                      <Label htmlFor=\"service\" className=\"flex items-center mb-2\">\n                        <Building className=\"h-4 w-4 mr-2\" />\n                        Service Needed *\n                      </Label>\n                      <select\n                        id=\"service\"\n                        name=\"service\"\n                        value={formData.service}\n                        onChange={handleInputChange}\n                        required\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"\">Select a service</option>\n                        {services.map((service, index) => (\n                          <option key={index} value={service}>\n                            {service}\n                          </option>\n                        ))}\n                      </select>\n                    </div>\n                  </div>\n\n                  <div className=\"grid md:grid-cols-2 gap-4\">\n                    <div>\n                      <Label htmlFor=\"budget\" className=\"mb-2 block\">\n                        Project Budget (NPR)\n                      </Label>\n                      <select\n                        id=\"budget\"\n                        name=\"budget\"\n                        value={formData.budget}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"\">Select budget range</option>\n                        <option value=\"under-1m\">Under 10 Lakh</option>\n                        <option value=\"1m-5m\">10 Lakh - 50 Lakh</option>\n                        <option value=\"5m-1cr\">50 Lakh - 1 Crore</option>\n                        <option value=\"1cr-plus\">1 Crore+</option>\n                      </select>\n                    </div>\n                    <div>\n                      <Label htmlFor=\"timeline\" className=\"flex items-center mb-2\">\n                        <Calendar className=\"h-4 w-4 mr-2\" />\n                        Project Timeline\n                      </Label>\n                      <select\n                        id=\"timeline\"\n                        name=\"timeline\"\n                        value={formData.timeline}\n                        onChange={handleInputChange}\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                      >\n                        <option value=\"\">Select timeline</option>\n                        <option value=\"asap\">ASAP</option>\n                        <option value=\"1-3months\">1-3 months</option>\n                        <option value=\"3-6months\">3-6 months</option>\n                        <option value=\"6months-plus\">6+ months</option>\n                      </select>\n                    </div>\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"message\" className=\"mb-2 block\">\n                      Project Details *\n                    </Label>\n                    <Textarea\n                      id=\"message\"\n                      name=\"message\"\n                      value={formData.message}\n                      onChange={handleInputChange}\n                      required\n                      rows={5}\n                      placeholder=\"Please describe your project requirements, location, and any specific needs...\"\n                    />\n                  </div>\n\n                  <Button \n                    type=\"submit\" \n                    className=\"w-full bg-blue-600 hover:bg-blue-700\" \n                    size=\"lg\"\n                    disabled={isSubmitting}\n                  >\n                    {isSubmitting ? (\n                      \"Sending Message...\"\n                    ) : (\n                      <>\n                        <Send className=\"h-5 w-5 mr-2\" />\n                        Send Message & Get Quote\n                      </>\n                    )}\n                  </Button>\n                </form>\n              )}\n            </Card>\n\n            {/* Map and Additional Info */}\n            <div className=\"space-y-8\">\n              {/* Map Placeholder */}\n              <Card className=\"p-8\">\n                <CardHeader className=\"p-0 mb-6\">\n                  <CardTitle className=\"text-xl text-slate-900 flex items-center\">\n                    <MapPin className=\"h-5 w-5 mr-2 text-blue-600\" />\n                    Our Location\n                  </CardTitle>\n                </CardHeader>\n                <div className=\"bg-slate-200 rounded-lg h-64 flex items-center justify-center\">\n                  <div className=\"text-center\">\n                    <MapPin className=\"h-12 w-12 text-slate-400 mx-auto mb-2\" />\n                    <p className=\"text-slate-600\">Interactive Map</p>\n                    <p className=\"text-sm text-slate-500\">Kathmandu, Nepal</p>\n                  </div>\n                </div>\n                <div className=\"mt-6 space-y-3\">\n                  <div className=\"flex items-start space-x-3\">\n                    <MapPin className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n                    <div>\n                      <p className=\"font-medium text-slate-900\">Head Office</p>\n                      <p className=\"text-slate-600\">Ratna Park, Kathmandu, Nepal</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-start space-x-3\">\n                    <Building className=\"h-5 w-5 text-blue-600 mt-0.5\" />\n                    <div>\n                      <p className=\"font-medium text-slate-900\">Project Office</p>\n                      <p className=\"text-slate-600\">Multiple locations across Nepal</p>\n                    </div>\n                  </div>\n                </div>\n              </Card>\n\n              {/* Quick Contact */}\n              <Card className=\"p-6 bg-blue-600 text-white\">\n                <CardHeader className=\"p-0 mb-4\">\n                  <CardTitle className=\"text-xl\">Need Immediate Assistance?</CardTitle>\n                </CardHeader>\n                <CardContent className=\"p-0\">\n                  <p className=\"mb-4\">\n                    For urgent construction needs or emergency repairs, call us directly.\n                  </p>\n                  <div className=\"space-y-2\">\n                    <Button asChild variant=\"secondary\" className=\"w-full\">\n                      <a href=\"tel:+977-1-4567890\">\n                        <Phone className=\"h-4 w-4 mr-2\" />\n                        Call Now: +977-1-4567890\n                      </a>\n                    </Button>\n                    <Button asChild variant=\"outline\" className=\"w-full border-white text-white hover:bg-white hover:text-blue-600\">\n                      <a href=\"mailto:<EMAIL>\">\n                        <Mail className=\"h-4 w-4 mr-2\" />\n                        Email Us\n                      </a>\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </div>\n      </section>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AATA;;;;;;;;;;AAsBA,MAAM,cAAc;IAClB;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAAkB;SAAiB;QAC7C,aAAa;IACf;IACA;QACE,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;QACP,SAAS;YAAC;YAA8B;SAAiC;QACzE,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,SAAM;QACZ,OAAO;QACP,SAAS;YAAC;YAAoB;SAAkB;QAChD,aAAa;IACf;IACA;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,SAAS;YAAC;YAA8B;SAAyB;QACjE,aAAa;IACf;CACD;AAED,MAAM,WAAW;IACf;IACA;IACA;IACA;IACA;IACA;CACD;AAEc,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,aAAa;QACb,QAAQ;QACR,UAAU;QACV,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM;QAChC,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE;YACV,CAAC;IACH;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,gBAAgB;QAEhB,2BAA2B;QAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;QAEjD,gBAAgB;QAChB,eAAe;QAEf,6BAA6B;QAC7B,WAAW;YACT,eAAe;YACf,YAAY;gBACV,MAAM;gBACN,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,aAAa;gBACb,QAAQ;gBACR,UAAU;gBACV,SAAS;YACX;QACF,GAAG;IACL;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iIAAA,CAAA,QAAK;gCAAC,WAAU;;kDACf,8OAAC,wNAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAG5C,8OAAC;gCAAG,WAAU;;oCAAsC;oCACjC;kDACjB,8OAAC;wCAAK,WAAU;kDAAkB;;;;;;;;;;;;0CAEpC,8OAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;;;;;;;;;;;0BAS5D,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,gIAAA,CAAA,OAAI;gCAAa,WAAU;0CAC1B,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,KAAK,IAAI;oDAAC,WAAU;;;;;;;;;;;;;;;;sDAGzB,8OAAC;4CAAG,WAAU;sDACX,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAI,WAAU;sDACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,8OAAC;oDAAoB,WAAU;8DAC5B;mDADK;;;;;;;;;;sDAKZ,8OAAC;4CAAE,WAAU;sDAA0B,KAAK,WAAW;;;;;;;;;;;;+BAjBhD;;;;;;;;;;;;;;;;;;;;0BA0BnB,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,gIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;;0DACpB,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,kMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAA+B;;;;;;;0DAGjD,8OAAC;gDAAE,WAAU;0DAAsB;;;;;;;;;;;;oCAKpC,4BACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC;gDAAG,WAAU;0DAA4C;;;;;;0DAG1D,8OAAC;gDAAE,WAAU;0DAAiB;;;;;;;;;;;6DAKhC,8OAAC;wCAAK,UAAU;wCAAc,WAAU;;0DACtC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAO,WAAU;;kFAC9B,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,IAAI;gEACpB,UAAU;gEACV,QAAQ;gEACR,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;;kFAC/B,8OAAC,kMAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,aAAY;;;;;;;;;;;;;;;;;;0DAKlB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAQ,WAAU;;kFAC/B,8OAAC,oMAAA,CAAA,QAAK;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGpC,8OAAC,iIAAA,CAAA,QAAK;gEACJ,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,KAAK;gEACrB,UAAU;gEACV,QAAQ;gEACR,aAAY;;;;;;;;;;;;kEAGhB,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;;kFACjC,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,OAAO;gEACvB,UAAU;gEACV,QAAQ;gEACR,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;oEAChB,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4EAAmB,OAAO;sFACxB;2EADU;;;;;;;;;;;;;;;;;;;;;;;0DAQrB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAS,WAAU;0EAAa;;;;;;0EAG/C,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,MAAM;gEACtB,UAAU;gEACV,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAW;;;;;;kFACzB,8OAAC;wEAAO,OAAM;kFAAQ;;;;;;kFACtB,8OAAC;wEAAO,OAAM;kFAAS;;;;;;kFACvB,8OAAC;wEAAO,OAAM;kFAAW;;;;;;;;;;;;;;;;;;kEAG7B,8OAAC;;0EACC,8OAAC,iIAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAW,WAAU;;kFAClC,8OAAC,0MAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGvC,8OAAC;gEACC,IAAG;gEACH,MAAK;gEACL,OAAO,SAAS,QAAQ;gEACxB,UAAU;gEACV,WAAU;;kFAEV,8OAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,8OAAC;wEAAO,OAAM;kFAAO;;;;;;kFACrB,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAY;;;;;;kFAC1B,8OAAC;wEAAO,OAAM;kFAAe;;;;;;;;;;;;;;;;;;;;;;;;0DAKnC,8OAAC;;kEACC,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAAa;;;;;;kEAGhD,8OAAC,oIAAA,CAAA,WAAQ;wDACP,IAAG;wDACH,MAAK;wDACL,OAAO,SAAS,OAAO;wDACvB,UAAU;wDACV,QAAQ;wDACR,MAAM;wDACN,aAAY;;;;;;;;;;;;0DAIhB,8OAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,WAAU;gDACV,MAAK;gDACL,UAAU;0DAET,eACC,qCAEA;;sEACE,8OAAC,kMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;0CAU7C,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;;sEACnB,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;wDAA+B;;;;;;;;;;;;0DAIrD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;4DAAE,WAAU;sEAAiB;;;;;;sEAC9B,8OAAC;4DAAE,WAAU;sEAAyB;;;;;;;;;;;;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;0EAClB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAiB;;;;;;;;;;;;;;;;;;kEAGlC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0MAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;0EACpB,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAA6B;;;;;;kFAC1C,8OAAC;wEAAE,WAAU;kFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAOtC,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,8OAAC,gIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,8OAAC,gIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,8OAAC;wDAAE,WAAU;kEAAO;;;;;;kEAGpB,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,SAAQ;gEAAY,WAAU;0EAC5C,cAAA,8OAAC;oEAAE,MAAK;;sFACN,8OAAC,oMAAA,CAAA,QAAK;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;0EAItC,8OAAC,kIAAA,CAAA,SAAM;gEAAC,OAAO;gEAAC,SAAQ;gEAAU,WAAU;0EAC1C,cAAA,8OAAC;oEAAE,MAAK;;sFACN,8OAAC,kMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;wEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAazD", "debugId": null}}]}